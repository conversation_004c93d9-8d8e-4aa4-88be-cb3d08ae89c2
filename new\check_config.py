#!/usr/bin/env python3
"""
配置检查工具 - 检查config和prompts的路径和内容
"""

import os
import json
import sys

def check_config_files():
    """检查配置文件"""
    print("=== 配置文件检查 ===")
    
    # 检查config文件夹
    config_dir = "config"
    if os.path.exists(config_dir):
        print(f"✓ 配置文件夹存在: {config_dir}")
        
        # 列出config文件夹内容
        config_files = os.listdir(config_dir)
        print(f"  配置文件: {config_files}")
        
        # 检查MCP配置文件
        mcp_config_path = os.path.join(config_dir, "llm_search_mcp_config.json")
        if os.path.exists(mcp_config_path):
            print(f"✓ MCP配置文件存在: {mcp_config_path}")
            
            try:
                with open(mcp_config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                print("✓ MCP配置文件格式正确")
                
                # 检查配置结构
                required_keys = ['servers', 'mcpServers', 'tools', 'server_config']
                for key in required_keys:
                    if key in config:
                        print(f"  ✓ 包含 {key}")
                    else:
                        print(f"  ✗ 缺少 {key}")
                
                return config
                
            except json.JSONDecodeError as e:
                print(f"✗ MCP配置文件JSON格式错误: {e}")
                return None
        else:
            print(f"✗ MCP配置文件不存在: {mcp_config_path}")
            return None
    else:
        print(f"✗ 配置文件夹不存在: {config_dir}")
        return None

def check_prompts_module():
    """检查prompts模块"""
    print("\n=== Prompts模块检查 ===")
    
    # 检查prompts文件夹
    prompts_dir = "src/prompts"
    if os.path.exists(prompts_dir):
        print(f"✓ Prompts文件夹存在: {prompts_dir}")
        
        # 列出prompts文件
        prompts_files = os.listdir(prompts_dir)
        print(f"  Prompts文件: {prompts_files}")
        
        # 检查关键文件
        key_files = ['__init__.py', 'base.py', 'prompts_en.py', 'prompts_zh.py']
        for file in key_files:
            file_path = os.path.join(prompts_dir, file)
            if os.path.exists(file_path):
                print(f"  ✓ {file}")
            else:
                print(f"  ✗ {file}")
        
        # 尝试导入prompts模块
        try:
            sys.path.append('.')
            from src.prompts import QUERY_EXPAND_PROMPT_WITH_ABSTRACT
            print("✓ Prompts模块导入成功")
            return True
        except ImportError as e:
            print(f"✗ Prompts模块导入失败: {e}")
            return False
    else:
        print(f"✗ Prompts文件夹不存在: {prompts_dir}")
        return False

def check_api_keys(config):
    """检查API密钥配置"""
    print("\n=== API密钥检查 ===")
    
    if not config:
        print("✗ 无法检查API密钥，配置文件未加载")
        return False
    
    # 从配置文件中获取环境变量设置
    server_env = config.get('servers', {}).get('llm_search_server', {}).get('env', {})
    
    # 检查配置文件中的API密钥设置
    api_keys = {
        'OPENAI_API_KEY': 'OpenAI API密钥',
        'SERP_API_KEY': 'SerpAPI密钥',
        'GOOGLE_API_KEY': 'Google API密钥',
        'BING_SEARCH_V7_SUBSCRIPTION_KEY': 'Bing搜索API密钥'
    }
    
    print("配置文件中的API密钥设置:")
    config_keys_set = 0
    for key, desc in api_keys.items():
        if key in server_env:
            value = server_env[key]
            if value and value.strip():
                print(f"  ✓ {key}: 已在配置文件中设置 ({desc})")
                config_keys_set += 1
            else:
                print(f"  ✗ {key}: 配置文件中为空 ({desc})")
        else:
            print(f"  ✗ {key}: 配置文件中缺失 ({desc})")
    
    # 检查环境变量中的API密钥
    print("\n环境变量中的API密钥设置:")
    env_keys_set = 0
    for key, desc in api_keys.items():
        env_value = os.getenv(key)
        if env_value and env_value.strip():
            print(f"  ✓ {key}: 已在环境变量中设置 ({desc})")
            env_keys_set += 1
        else:
            print(f"  ✗ {key}: 环境变量中未设置 ({desc})")
    
    # 总结
    print(f"\n总结:")
    print(f"  配置文件中设置的密钥: {config_keys_set}/{len(api_keys)}")
    print(f"  环境变量中设置的密钥: {env_keys_set}/{len(api_keys)}")
    
    # 检查必需的密钥
    required_keys = ['OPENAI_API_KEY', 'SERP_API_KEY']
    missing_required = []
    
    for key in required_keys:
        config_has = server_env.get(key, '').strip()
        env_has = os.getenv(key, '').strip()
        if not config_has and not env_has:
            missing_required.append(key)
    
    if missing_required:
        print(f"⚠️ 缺少必需的API密钥: {missing_required}")
        return False
    else:
        print("✓ 所有必需的API密钥都已设置")
        return True

def check_server_client_paths():
    """检查Server和Client中的配置路径"""
    print("\n=== Server和Client配置路径检查 ===")
    
    # 检查Server配置路径
    server_file = "src/search/llm_search_mcp_server.py"
    if os.path.exists(server_file):
        print(f"✓ Server文件存在: {server_file}")
        
        with open(server_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查配置路径
        if "llm_search_mcp_config.json" in content:
            print("  ✓ Server中包含正确的配置路径")
        else:
            print("  ✗ Server中配置路径可能不正确")
        
        # 检查配置加载函数
        if "load_server_config" in content:
            print("  ✓ Server包含配置加载函数")
        else:
            print("  ✗ Server缺少配置加载函数")
    else:
        print(f"✗ Server文件不存在: {server_file}")
    
    # 检查Client配置路径
    client_file = "src/search/llm_search_mcp_client.py"
    if os.path.exists(client_file):
        print(f"✓ Client文件存在: {client_file}")
        
        with open(client_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查配置路径
        if "config/llm_search_mcp_config.json" in content:
            print("  ✓ Client中包含正确的配置路径")
        else:
            print("  ✗ Client中配置路径可能不正确")
    else:
        print(f"✗ Client文件不存在: {client_file}")

def print_setup_recommendations():
    """打印设置建议"""
    print("\n" + "="*60)
    print("设置建议")
    print("="*60)
    print("""
🔧 API密钥设置方法:

方法1: 设置环境变量 (推荐)
export OPENAI_API_KEY="your_openai_key"
export SERP_API_KEY="your_serp_key"

方法2: 修改配置文件
编辑 config/llm_search_mcp_config.json
在 servers.llm_search_server.env 中设置API密钥

🔑 必需的API密钥:
- OPENAI_API_KEY: OpenAI API密钥 (用于LLM调用)
- SERP_API_KEY: SerpAPI密钥 (用于Google搜索)

🔑 可选的API密钥:
- GOOGLE_API_KEY: Google API密钥 (备用LLM)
- BING_SEARCH_V7_SUBSCRIPTION_KEY: Bing搜索API密钥 (备用搜索)

📝 获取API密钥:
- OpenAI: https://platform.openai.com/api-keys
- SerpAPI: https://serpapi.com/
- Google: https://console.cloud.google.com/
- Bing: https://www.microsoft.com/en-us/bing/apis/bing-web-search-api

🧪 测试步骤:
1. 设置API密钥
2. 运行: python check_config.py
3. 运行: python test_mcp_architecture_basic.py
4. 运行: python test_complete_mcp_architecture.py
    """)

def main():
    """主函数"""
    print("MCP LLM Search 配置检查工具")
    print("="*50)
    
    # 检查配置文件
    config = check_config_files()
    
    # 检查prompts模块
    prompts_ok = check_prompts_module()
    
    # 检查API密钥
    api_keys_ok = check_api_keys(config)
    
    # 检查Server和Client路径
    check_server_client_paths()
    
    # 打印设置建议
    print_setup_recommendations()
    
    # 总结
    print("\n" + "="*60)
    print("检查总结")
    print("="*60)
    
    if config and prompts_ok:
        print("✅ 基础配置检查通过")
        if api_keys_ok:
            print("✅ API密钥配置完整")
            print("🎉 可以运行完整的MCP架构测试!")
        else:
            print("⚠️ API密钥配置不完整")
            print("📝 请按照上述建议设置API密钥")
    else:
        print("❌ 基础配置检查失败")
        print("📝 请检查配置文件和prompts模块")
    
    print("\n📚 相关文件:")
    print("  - 配置文件: config/llm_search_mcp_config.json")
    print("  - 基础测试: python test_mcp_architecture_basic.py")
    print("  - 完整测试: python test_complete_mcp_architecture.py")
    print("  - API密钥设置: python setup_api_keys.py")

if __name__ == "__main__":
    main()
