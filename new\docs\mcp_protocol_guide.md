# MCP协议详解与LLM搜索服务器实现指南

## MCP协议概述

**MCP (Model Context Protocol)** 是一个开放标准协议，用于在AI应用程序和外部数据源及工具之间建立安全、标准化的连接。

### 核心特性

1. **标准化接口**: 基于JSON-RPC 2.0协议
2. **工具调用**: 支持LLM调用外部工具和服务
3. **资源访问**: 提供对外部数据源的访问
4. **安全通信**: 通过stdio或其他传输方式进行安全通信
5. **异步支持**: 支持异步操作和并发处理

## 架构组件

### 1. MCP服务器 (MCP Server)
- 提供具体的工具和服务实现
- 处理来自客户端的请求
- 管理资源和状态

### 2. MCP客户端 (MCP Client)  
- 连接到MCP服务器
- 发送工具调用请求
- 处理服务器响应

### 3. LLM应用
- 使用MCP客户端调用外部工具
- 集成工具结果到对话流程
- 提供用户交互界面

## LLM搜索MCP服务器实现

### 配置文件结构

```json
{
  "servers": {
    "llm_search_server": {
      "command": "python",
      "args": ["-m", "src.search.llm_search_mcp_server"],
      "env": {
        "PYTHONPATH": ".",
        "OPENAI_API_KEY": "${OPENAI_API_KEY}",
        "GOOGLE_API_KEY": "${GOOGLE_API_KEY}",
        "SERP_API_KEY": "${SERP_API_KEY}"
      }
    }
  },
  "tools": {
    "generate_search_queries": {
      "description": "Generate optimized search queries for a given topic using LLM",
      "parameters": {
        "topic": {"type": "string", "description": "Research topic"},
        "description": {"type": "string", "description": "Optional context"},
        "model": {"type": "string", "default": "gemini-2.0-flash-thinking-exp-01-21"}
      }
    },
    "web_search": {
      "description": "Perform web search using generated queries",
      "parameters": {
        "queries": {"type": "array", "items": {"type": "string"}},
        "topic": {"type": "string"},
        "top_n": {"type": "integer", "default": 20},
        "engine": {"type": "string", "default": "google"}
      }
    },
    "full_search_pipeline": {
      "description": "Execute complete search pipeline",
      "parameters": {
        "topic": {"type": "string"},
        "description": {"type": "string"},
        "top_n": {"type": "integer", "default": 10},
        "model": {"type": "string", "default": "gemini-2.0-flash-thinking-exp-01-21"},
        "engine": {"type": "string", "default": "google"}
      }
    }
  }
}
```

### 服务器实现要点

#### 1. 工具注册
```python
@self.server.list_tools()
async def handle_list_tools() -> List[Tool]:
    return [
        Tool(
            name="generate_search_queries",
            description="Generate optimized search queries",
            inputSchema={...}
        )
    ]
```

#### 2. 工具调用处理
```python
@self.server.call_tool()
async def handle_call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
    if name == "generate_search_queries":
        return await self._generate_search_queries(arguments)
    # ... 其他工具处理
```

#### 3. LLM搜索集成
```python
def _get_llm_search_instance(self, model: str, engine: str) -> LLM_search:
    key = f"{model}_{engine}"
    if key not in self.llm_search_instances:
        self.llm_search_instances[key] = LLM_search(
            model=model,
            infer_type="OpenAI",
            engine=engine
        )
    return self.llm_search_instances[key]
```

## 客户端使用方式

### 1. 创建客户端连接
```python
from src.search.llm_search_mcp_client import create_llm_search_client

# 创建并连接客户端
client = await create_llm_search_client("config/llm_search_mcp_config.json")
```

### 2. 调用工具
```python
# 生成搜索查询
queries_result = await client.generate_search_queries(
    topic="machine learning optimization",
    description="Research on optimization techniques"
)

# 执行网络搜索
search_result = await client.web_search(
    queries=queries_result["queries"],
    topic="machine learning optimization",
    top_n=10
)

# 完整流水线
pipeline_result = await client.full_search_pipeline(
    topic="machine learning optimization",
    description="Research on optimization techniques",
    top_n=5
)
```

### 3. 断开连接
```python
await client.disconnect()
```

## 与LLM API调用的集成

### 1. RequestWrapper集成
```python
# 在MCP服务器中使用现有的RequestWrapper
from request import RequestWrapper

class LLMSearchMCPServer:
    def __init__(self):
        self.request_wrapper = RequestWrapper(
            model="gemini-2.0-flash-thinking-exp-01-21",
            infer_type="OpenAI"
        )
```

### 2. 异步处理
```python
async def _generate_search_queries(self, arguments: Dict[str, Any]) -> List[TextContent]:
    # 使用现有的LLM_search类
    llm_search = self._get_llm_search_instance(model=arguments.get("model"))
    queries = llm_search.get_queries(
        topic=arguments["topic"],
        description=arguments.get("description", "")
    )
    
    # 返回标准化的MCP响应
    return [TextContent(
        type="text",
        text=json.dumps({"queries": queries}, ensure_ascii=False, indent=2)
    )]
```

## 部署和运行

### 1. 启动MCP服务器
```bash
# 直接运行服务器
python -m src.search.llm_search_mcp_server

# 或通过配置启动
python -c "
import asyncio
from src.search.llm_search_mcp_server import main
asyncio.run(main())
"
```

### 2. 环境变量配置
```bash
export OPENAI_API_KEY="your-openai-key"
export GOOGLE_API_KEY="your-google-key"
export SERP_API_KEY="your-serpapi-key"
export BING_SEARCH_V7_SUBSCRIPTION_KEY="your-bing-key"
```

### 3. 客户端连接测试
```python
# 测试客户端连接
python -c "
import asyncio
from src.search.llm_search_mcp_client import example_usage
asyncio.run(example_usage())
"
```

## 错误处理和日志

### 1. 异常处理
```python
try:
    result = await client.full_search_pipeline(topic="test")
except Exception as e:
    logger.error(f"Search pipeline failed: {e}")
    # 处理错误
```

### 2. 日志配置
```python
import logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
```

## 扩展和自定义

### 1. 添加新工具
```python
# 在服务器中添加新工具
Tool(
    name="custom_analysis",
    description="Custom analysis tool",
    inputSchema={...}
)

# 实现工具逻辑
async def _custom_analysis(self, arguments: Dict[str, Any]) -> List[TextContent]:
    # 自定义实现
    pass
```

### 2. 集成其他服务
```python
# 可以集成项目中的其他模块
from src.hidden.mcp_server.digest_processor_server import DigestProcessor
from src.encode.encode_pipeline import EncodePipeline
```

这个MCP实现提供了一个完整的LLM搜索服务框架，可以轻松集成到现有的MapxReduceV3项目中，为LLM提供强大的搜索和分析能力。
