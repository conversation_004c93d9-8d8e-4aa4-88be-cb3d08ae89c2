# 修正后的Host架构详解

## 用户反馈的关键问题

> "我觉得你对host的理解有问题，host不应该本身拥有被暴露的工具信息，而是通过向client发出询问，client调用server得到工具信息后反馈，host仅仅是一个llm的调用，应该仅仅包含提示词和llm选择调用方式，然后在此基础上你需要注意对齐一下原始llm_Search的接口。有关搜索的底层setting都应该在server中实现或者调取读入"

## 修正前的问题

### ❌ 错误的Host实现
```python
class LLM_search:
    def __init__(self):
        # 错误：Host包含了工具信息
        self.conversation_agent = LLMConversationAgent()
        
        # 错误：Host包含搜索配置
        self.engine = engine
        self.each_query_result = each_query_result
        
        # 错误：Host检查API密钥
        self._check_api_keys()
    
    def get_queries(self):
        # 错误：Host通过Agent间接调用
        result = self.conversation_agent.execute_search_task(task_description)
```

### 问题分析
1. **Host拥有工具信息**：Host不应该知道有哪些工具
2. **Host包含业务逻辑**：搜索配置应该在Server中
3. **架构层次混乱**：Host → Agent → Client → Server 过于复杂
4. **职责不清**：Host承担了过多责任

## 修正后的正确实现

### ✅ 正确的Host实现
```python
class LLM_search:
    def __init__(self, model, engine, each_query_result, ...):
        # 正确：仅存储配置，传递给Server
        self.search_config = {
            "model": model,
            "engine": engine,
            "each_query_result": each_query_result,
            # ... 其他配置
        }
        
        # 正确：仅创建LLM请求包装器
        self.request_wrapper = RequestWrapper(model=model, infer_type=infer_type)
        
        # 正确：延迟初始化Client
        self._mcp_client = None
    
    async def _get_available_tools(self):
        """向Server询问可用工具信息"""
        client = await self._get_mcp_client()
        return await client.list_tools()
    
    def _create_tool_selection_prompt(self, task_description, available_tools):
        """创建工具选择提示词"""
        tools_info = "\n".join([
            f"- {tool['name']}: {tool['description']}"
            for tool in available_tools
        ])
        
        return f"""你是一个智能搜索工具选择器...
可用工具：
{tools_info}

用户任务：{task_description}

请选择最合适的工具..."""
    
    async def _llm_select_and_call_tool(self, task_description, **kwargs):
        """LLM选择并调用工具"""
        # 1. 向Server询问可用工具
        available_tools = await self._get_available_tools()
        
        # 2. LLM选择工具
        prompt = self._create_tool_selection_prompt(task_description, available_tools)
        response = await self.request_wrapper.async_request(...)
        
        # 3. 解析LLM响应并调用工具
        decision = json.loads(response)
        arguments = decision["arguments"]
        arguments.update(kwargs)  # 合并配置参数
        
        # 4. 通过Client调用Server工具
        client = await self._get_mcp_client()
        return await client.call_tool(decision["selected_tool"], arguments)
    
    def get_queries(self, topic, description=""):
        """保持原有接口"""
        return asyncio.run(self._async_get_queries(topic, description))
    
    async def _async_get_queries(self, topic, description=""):
        task_description = f"为主题'{topic}'生成搜索查询。描述：{description}"
        
        # LLM选择工具并调用，传递配置给Server
        result = await self._llm_select_and_call_tool(
            task_description,
            topic=topic,
            description=description,
            model=self.search_config["model"]  # 配置传递给Server
        )
        
        return result.get("queries", [])
```

## 正确的架构职责分离

### 🏠 Host (LLM调用器)
**职责**：
- ✅ 仅包含LLM调用逻辑和提示词
- ✅ 向Client询问Server的工具信息
- ✅ 使用LLM分析任务并选择合适的工具
- ✅ 通过Client调用Server工具
- ✅ 保持与原有LLM_search接口一致

**不包含**：
- ❌ 工具实现
- ❌ 搜索配置处理
- ❌ 业务逻辑
- ❌ API密钥检查

### 🔌 Client (通信桥梁)
**职责**：
- ✅ 纯粹的MCP协议通信
- ✅ 连接管理和消息传递
- ✅ 向Server询问工具信息
- ✅ 调用Server工具
- ✅ 返回Server响应

**不包含**：
- ❌ 业务逻辑
- ❌ 工具选择
- ❌ 配置管理

### 🖥️ Server (工具+配置实现)
**职责**：
- ✅ 实现具体的搜索工具
- ✅ 管理搜索引擎配置
- ✅ 处理LLM调用
- ✅ 执行业务逻辑
- ✅ API密钥管理
- ✅ 所有底层设置和配置

## 正确的调用流程

```
1. Pipeline调用Host方法
   ↓
2. Host向Client询问Server可用工具
   ↓
3. Host使用LLM分析任务并选择工具
   ↓
4. Host通过Client调用Server工具（传递配置）
   ↓
5. Server执行工具并返回结果
   ↓
6. Host处理结果并返回给Pipeline
```

## 关键修正点

### 1. Host不拥有工具信息
```python
# 修正前：Host包含工具信息
self.search_servers = {
    "llm_search_mcp": {
        "tools": ["generate_search_queries", "web_search", ...]
    }
}

# 修正后：Host动态询问工具信息
available_tools = await self._get_available_tools()
```

### 2. 配置传递给Server
```python
# 修正前：Host处理配置
self.engine = engine
self.each_query_result = each_query_result

# 修正后：配置传递给Server
result = await client.call_tool(
    "web_search",
    {
        "queries": queries,
        "engine": self.search_config["engine"],  # 传递给Server
        "top_n": self.search_config["each_query_result"]
    }
)
```

### 3. 简化架构层次
```python
# 修正前：Host → Agent → Client → Server
result = await self.conversation_agent.execute_search_task(task_description)

# 修正后：Host → Client → Server
client = await self._get_mcp_client()
result = await client.call_tool(tool_name, arguments)
```

### 4. 接口兼容性保持
```python
# 原有接口完全保持不变
def get_queries(self, topic: str, description: str = "") -> List[str]:
def web_search(self, query: str):
def batch_web_search(self, queries: List[str], topic: str, top_n: int = 20) -> List[str]:
def snippet_filter(self, topic: str, snippet: str) -> float:
def crawl_urls(self, urls: List[str], topic: str = "") -> dict:
```

## 使用方式

### 修正后的使用方式（接口不变）
```python
from src.search.llm_search_host import LLM_search

# 创建实例（接口不变）
llm_search = LLM_search(
    model="gemini-2.0-flash-thinking-exp-01-21",
    engine="google",
    each_query_result=10
)

# 使用方法（接口不变）
queries = llm_search.get_queries("机器学习", "研究机器学习算法")
urls = llm_search.batch_web_search(queries, "机器学习", 20)
```

### 内部工作流程（用户无感知）
1. Host向Server询问可用工具
2. Host使用LLM选择最合适的工具
3. Host将配置参数传递给Server
4. Server执行具体的搜索逻辑
5. Host返回结果给用户

## 总结

修正后的架构实现了用户反馈的所有要求：

✅ **Host不拥有工具信息**：通过Client向Server动态询问  
✅ **Host仅为LLM调用器**：只包含提示词和LLM选择逻辑  
✅ **配置在Server中**：所有搜索设置都在Server中实现  
✅ **接口对齐**：与原始LLM_search接口完全兼容  
✅ **职责清晰**：Host(LLM) → Client(通信) → Server(实现)  

这种架构既满足了正确的MCP设计原则，又保持了完全的向后兼容性。
