from typing import List, Dict
from gevent.lock import Semaphore
from .local import LocalRequest
from .openai import OpenAIRequest
from .google import GoogleRequest

import logging
import sys
import os

# 添加项目路径以导入memory_manager
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

try:
    from src.utils.memory_manager import add_conversation_to_memory, get_conversation_context
except ImportError:
    # 如果导入失败，提供空实现
    def add_conversation_to_memory(model, messages, response, metadata=None):
        pass
    def get_conversation_context(model, max_messages=10):
        return []

logger = logging.getLogger(__name__)



class RequestWrapper:
    _connection_semaphore = {}
    _calls_count = 0 # 用来统计api的调用次数
    _token_usage_history = [] # 用来统计每次调用时使用的token数

    def __init__(self, model="gemini-2.0-flash-thinking-exp-1219", infer_type="OpenAI", connection=20, port=None,
                 use_memory=True, max_context_messages=10):
        if not model:
            model = "gemini-2.0-flash-thinking-exp-1219"

        self.request_pool = None
        self.model = model
        self.use_memory = use_memory
        self.max_context_messages = max_context_messages
        self._connection_semaphore[model] = Semaphore(connection)

        if infer_type == "OpenAI":
            self.request_pool = OpenAIRequest(model=model)
        elif infer_type == "Google":
            self.request_pool = GoogleRequest(model=model)
        elif infer_type == "local":
            self.request_pool = LocalRequest(port=port)
        else:
            raise ValueError(
                f"Invalid infer_type: {infer_type}, should be OpenAI or local"
            )

    def completion(self, message, **kwargs):
        # 处理输入消息格式
        if isinstance(message, str):
            message = [{"role": "user", "content": message}]
        elif isinstance(message, List):
            # 验证消息格式（简化版本，只支持标准user-assistant对话）
            for m in message:
                if not isinstance(m, Dict) or "role" not in m or not isinstance(m["role"], str):
                    raise ValueError("Each message must be a dict with 'role' field")

                role = m["role"]
                # 所有消息都必须有content字段
                if "content" not in m or not isinstance(m["content"], str):
                    raise ValueError(f"{role} messages must have 'content' field with string value")

        # 如果启用了记忆功能，添加历史上下文
        final_messages = message.copy()
        if self.use_memory:
            try:
                context_messages = get_conversation_context(self.model, self.max_context_messages)
                if context_messages:
                    # 将历史上下文插入到当前消息之前
                    final_messages = context_messages + message
                    logger.debug(f"Added {len(context_messages)} context messages for model {self.model}")
            except Exception as e:
                logger.warning(f"Failed to load conversation context: {e}")

        # 执行LLM调用
        if self.model in self._connection_semaphore:
            with self._connection_semaphore[self.model]:
                logger.debug(f"Acquired semaphore for {self.model} (remain={self._connection_semaphore[self.model].counter})")
                result, token_usage = self.request_pool.completion(final_messages, **kwargs)
        else:
            result, token_usage = self.request_pool.completion(final_messages, **kwargs)

        self._calls_count += 1
        self._token_usage_history.append(token_usage)

        # 保存对话历史到内存
        if self.use_memory and result:
            try:
                metadata = {
                    "token_usage": token_usage,
                    "model": self.model,
                    "kwargs": kwargs,
                    "call_count": self._calls_count
                }
                add_conversation_to_memory(self.model, message, result, metadata)
                logger.debug(f"Saved conversation to memory for model {self.model}")
            except Exception as e:
                logger.warning(f"Failed to save conversation to memory: {e}")

        logger.debug(f"Requesting completion received")
        if not result:
            raise ValueError(
                f"Requesting completion failed, return with empty result, message length: {len(str(message))}"
            )
        return result

    def get_conversation_history(self, limit=None):
        """获取当前模型的对话历史"""
        if not self.use_memory:
            return []
        try:
            from src.utils.memory_manager import get_memory_manager
            memory_manager = get_memory_manager()
            return memory_manager.get_conversation_history(self.model, limit)
        except Exception as e:
            logger.warning(f"Failed to get conversation history: {e}")
            return []

    def clear_conversation_history(self):
        """清除当前模型的对话历史"""
        if not self.use_memory:
            return
        try:
            from src.utils.memory_manager import get_memory_manager
            memory_manager = get_memory_manager()
            memory_manager.clear_model_history(self.model)
            logger.info(f"Cleared conversation history for model {self.model}")
        except Exception as e:
            logger.warning(f"Failed to clear conversation history: {e}")

    def get_memory_statistics(self):
        """获取内存使用统计"""
        if not self.use_memory:
            return {}
        try:
            from src.utils.memory_manager import get_memory_manager
            memory_manager = get_memory_manager()
            return memory_manager.get_statistics()
        except Exception as e:
            logger.warning(f"Failed to get memory statistics: {e}")
            return {}

    def export_conversation_history(self, output_file):
        """导出对话历史到文件"""
        if not self.use_memory:
            return False
        try:
            from src.utils.memory_manager import get_memory_manager
            memory_manager = get_memory_manager()
            memory_manager.export_history(self.model, output_file)
            return True
        except Exception as e:
            logger.error(f"Failed to export conversation history: {e}")
            return False

    async def async_request(self, messages, **kwargs):
        """异步版本的completion方法，与同步版本功能相同"""
        import asyncio
        import functools

        # 在线程池中执行同步的completion方法
        loop = asyncio.get_event_loop()
        # 使用functools.partial来传递关键字参数
        func = functools.partial(self.completion, messages, **kwargs)
        return await loop.run_in_executor(None, func)
