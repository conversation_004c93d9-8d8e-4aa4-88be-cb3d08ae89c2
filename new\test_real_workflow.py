#!/usr/bin/env python3
"""
真实的端到端测试：以"多模态推理"为主题执行完整的analyse工作流
每一步都打印详细的输入输出，确保流程正确
"""

import asyncio
import json
import logging
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.search.analyse import AnalyseInterface

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def print_separator(title):
    """打印分隔符"""
    print("\n" + "=" * 80)
    print(f"  {title}")
    print("=" * 80)

def print_step(step_num, description):
    """打印步骤"""
    print(f"\n🔸 步骤 {step_num}: {description}")
    print("-" * 60)

def print_input_output(input_data, output_data, step_name):
    """打印输入输出"""
    print(f"\n📥 {step_name} - 输入:")
    if isinstance(input_data, dict):
        print(json.dumps(input_data, ensure_ascii=False, indent=2))
    else:
        print(str(input_data))
    
    print(f"\n📤 {step_name} - 输出:")
    if isinstance(output_data, dict):
        print(json.dumps(output_data, ensure_ascii=False, indent=2))
    else:
        print(str(output_data))

async def test_real_multimodal_reasoning_workflow():
    """真实测试：多模态推理主题的完整工作流"""
    
    print_separator("真实端到端测试：多模态推理文献分析")
    
    # 测试参数
    topic = "多模态推理"
    description = "研究多模态推理技术，包括视觉-语言理解、跨模态融合、多模态大模型等"
    target_papers = 5
    
    print(f"🎯 测试主题: {topic}")
    print(f"📝 主题描述: {description}")
    print(f"📊 目标文献数量: {target_papers}")
    
    try:
        print_step(1, "初始化AnalyseInterface")
        
        analyse_interface = AnalyseInterface(
            base_dir="new/real_test_output",
            max_interaction_rounds=8,  # 增加轮次以确保完整执行
            llm_model="chatgpt-4o-latest",  # 使用配置文件中的正确模型
            llm_infer_type="OpenAI"
        )
        
        print("✅ AnalyseInterface初始化成功")
        print(f"   - 基础目录: {analyse_interface.base_dir}")
        print(f"   - 最大交互轮次: {analyse_interface.max_interaction_rounds}")
        print(f"   - LLM模型: {analyse_interface.llm_model}")
        
        print_step(2, "执行完整的analyse工作流")
        
        # 执行完整工作流，并捕获所有中间过程
        print("🚀 开始执行analyse工作流...")
        
        # 手动执行每个步骤以便详细观察
        print_step(2.1, "记忆初始化")
        analyse_interface.conversation_history.clear()
        print("✅ 对话历史已清空")
        
        print_step(2.2, "第一轮交互：主题扩写")
        user_msg_1 = f"请扩写主题：{topic}。原始描述：{description}"
        
        print_input_output(
            {"user_message": user_msg_1},
            "等待LLM响应...",
            "主题扩写请求"
        )
        
        expanded_topic = await analyse_interface._llm_interaction_round_1(user_msg_1)
        
        print_input_output(
            {"user_message": user_msg_1},
            {"expanded_topic": expanded_topic},
            "主题扩写结果"
        )
        
        print_step(2.3, "第二轮交互：开始文献调研")
        user_msg_2 = f"请进行文献调研和下载，目标获取{target_papers}篇相关文献"
        
        print_input_output(
            {"user_message": user_msg_2},
            "等待LLM响应...",
            "文献调研请求"
        )
        
        assistant_response = await analyse_interface._llm_interaction_tool_rounds(user_msg_2)
        
        print_input_output(
            {"user_message": user_msg_2},
            {"assistant_response": assistant_response},
            "文献调研决策"
        )
        
        print_step(2.4, "工具调用循环（第3轮到第n轮）")
        
        max_rounds = 8  # 增加最大轮次
        current_round = 3
        tool_call_count = 0
        
        while current_round <= max_rounds:
            print(f"\n🔄 第{current_round}轮交互 (工具调用 #{tool_call_count + 1})")
            
            # 解析工具指令
            tool_instruction = analyse_interface._parse_tool_instruction(assistant_response)
            
            print_input_output(
                {"assistant_response": assistant_response},
                {"tool_instruction": tool_instruction},
                f"第{current_round}轮工具指令解析"
            )
            
            if tool_instruction is None:
                print("🏁 LLM决定结束工具调用循环")
                break
            
            # 执行工具调用
            tool_name = tool_instruction.get('tool_name', 'unknown')
            tool_args = tool_instruction.get('arguments', {})
            
            print(f"⚙️ 执行工具: {tool_name}")
            print(f"📋 工具参数: {json.dumps(tool_args, ensure_ascii=False, indent=2)}")
            
            tool_result = await analyse_interface._execute_mcp_tool(tool_instruction)
            
            print_input_output(
                {
                    "tool_name": tool_name,
                    "arguments": tool_args
                },
                {"tool_result": tool_result},
                f"第{current_round}轮工具执行"
            )
            
            tool_call_count += 1
            
            # 下一轮交互
            user_msg = f"工具调用结果：{tool_result}"
            
            print_input_output(
                {"user_message": user_msg},
                "等待LLM响应...",
                f"第{current_round}轮结果反馈"
            )
            
            assistant_response = await analyse_interface._llm_interaction_tool_rounds(user_msg)
            
            print_input_output(
                {"user_message": user_msg},
                {"assistant_response": assistant_response},
                f"第{current_round}轮LLM响应"
            )
            
            current_round += 1
        
        print_step(2.5, "提取最终结果")
        
        results = analyse_interface._extract_final_results()
        
        print_input_output(
            {"conversation_history_length": len(analyse_interface.conversation_history)},
            {"final_results": results, "result_count": len(results)},
            "最终结果提取"
        )
        
        print_step(3, "分析对话历史")
        
        print(f"📚 对话历史总长度: {len(analyse_interface.conversation_history)}")
        print(f"🔧 工具调用总次数: {tool_call_count}")
        
        print("\n📋 对话历史详情:")
        for i, msg in enumerate(analyse_interface.conversation_history):
            role = msg.get("role", "unknown")
            content = msg.get("content", "")
            content_preview = content[:150] + "..." if len(content) > 150 else content
            print(f"  {i+1:2d}. [{role:9s}] {content_preview}")
        
        print_step(4, "测试结果总结")
        
        print(f"✅ 工作流执行完成")
        print(f"   - 总交互轮次: {current_round - 1}")
        print(f"   - 工具调用次数: {tool_call_count}")
        print(f"   - 最终结果数量: {len(results)}")
        print(f"   - 对话历史长度: {len(analyse_interface.conversation_history)}")
        
        # 检查是否有实际的搜索结果
        has_search_results = any("url" in str(msg.get("content", "")).lower() for msg in analyse_interface.conversation_history)
        has_paper_results = any("paper" in str(msg.get("content", "")).lower() or "article" in str(msg.get("content", "")).lower() for msg in analyse_interface.conversation_history)
        
        print(f"   - 包含搜索结果: {'是' if has_search_results else '否'}")
        print(f"   - 包含论文信息: {'是' if has_paper_results else '否'}")
        
        if tool_call_count > 0:
            print("🎉 真实工作流测试成功！")
            return True
        else:
            print("⚠️ 工作流执行但未进行工具调用")
            return False
            
    except Exception as e:
        print(f"❌ 真实工作流测试失败: {e}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")
        return False

async def main():
    """主测试函数"""
    success = await test_real_multimodal_reasoning_workflow()
    
    if success:
        print("\n🎉 真实端到端测试全部通过!")
        return 0
    else:
        print("\n❌ 真实端到端测试失败!")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
