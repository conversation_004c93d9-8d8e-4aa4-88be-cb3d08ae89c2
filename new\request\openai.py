import os
from openai import OpenAI, InternalServerError, RateLimitError, APIError
from tenacity import (
    retry,
    stop_after_attempt,
    wait_random_exponential,
    before_sleep_log,
    retry_if_exception_type
)
import logging
logger = logging.getLogger(__name__)


class OpenAIRequest:
    def __init__(self, model):
        # 获取API配置，优先使用环境变量，然后使用配置文件
        api_key = os.environ.get("OPENAI_API_KEY")
        base_url = os.environ.get("OPENAI_API_BASE") or os.environ.get("OPENAI_BASE_URL")

        # 如果环境变量没有设置，尝试从配置文件加载
        if not api_key or not base_url:
            try:
                import json
                config_path = os.path.join(os.path.dirname(__file__), '..', 'config', 'environment_config.json')
                if os.path.exists(config_path):
                    with open(config_path, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                    openai_config = config.get('api_keys', {}).get('openai', {})
                    if not api_key:
                        api_key = openai_config.get('api_key')
                    if not base_url:
                        base_url = openai_config.get('base_url')
            except Exception as e:
                logger.warning(f"Failed to load config file: {e}")

        # 设置默认值
        if not api_key:
            api_key = "sk-qRnt09M8Ncvl0Dwk1aA374833a6a4dB5B6A6De2e1901D146"
        if not base_url:
            base_url = "https://api.shubiaobiao.cn/v1"

        logger.debug(f"OpenAI client config - API Key: {api_key[:10]}..., Base URL: {base_url}")

        self.client = OpenAI(
            api_key=api_key,
            base_url=base_url,
        )
        self.model = model

    @retry(
        wait=wait_random_exponential(multiplier=2, max=60),
        stop=stop_after_attempt(100),
        retry=retry_if_exception_type((RateLimitError, InternalServerError, APIError)) # 如果不是这几个错就不retry了
        )
    def completion(self, messages, **kwargs):
        try:
            response = self.client.chat.completions.create(
                model=self.model, messages=messages, **kwargs
            )
            # 新增检查：确保响应包含有效的 choices 数据
            if not response.choices or len(response.choices) == 0:
                error_msg = "OpenAI API returned empty choices in response"
                logger.debug(error_msg)
                raise ValueError(error_msg)
            answer = response.choices[0].message.content
            token_usage = response.usage

        except RateLimitError as e:
            logger.warning(f"Rate limit exceeded in OpenAIRequest.completion: {e}")
            raise 
        except InternalServerError as e:
            logger.warning(f"Internal server error in OpenAIRequest.completion: {e}")
            # logger.warning(f"Prompt: {messages}")
            raise 
        except Exception as e:
            logger.error(f"Unexpected error in OpenAIRequest.completion: {e}. messages: \n{messages}")
            raise 
                
        return answer, token_usage
