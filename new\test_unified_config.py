#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试统一配置系统
验证所有模块是否正确从environment_config.json读取配置
"""

import os
import sys
import json
import logging

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.search.analyse import AnalyseInterface
from src.search.LLM_search import LLM_search

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_config_loading():
    """测试配置加载"""
    print("=" * 60)
    print("🧪 测试统一配置系统")
    print("=" * 60)
    
    # 1. 测试AnalyseInterface配置
    print("\n1️⃣ 测试AnalyseInterface配置:")
    try:
        analyse = AnalyseInterface()
        print(f"   ✅ 模型: {analyse.llm_model}")
        print(f"   ✅ 推理类型: {analyse.llm_infer_type}")
        print(f"   ✅ 最大交互轮数: {analyse.max_interaction_rounds}")
        print(f"   ✅ 最大上下文消息数: {analyse.llm_wrapper.max_context_messages}")
    except Exception as e:
        print(f"   ❌ AnalyseInterface配置测试失败: {e}")
    
    # 2. 测试LLM_search配置
    print("\n2️⃣ 测试LLM_search配置:")
    try:
        llm_search = LLM_search()
        print(f"   ✅ 模型: {llm_search.model}")
        print(f"   ✅ 搜索引擎: {llm_search.engine}")
        print(f"   ✅ 每个查询结果数: {llm_search.each_query_result}")
        print(f"   ✅ 最大工作线程: {llm_search.max_workers}")
    except Exception as e:
        print(f"   ❌ LLM_search配置测试失败: {e}")
    
    # 3. 测试配置文件内容
    print("\n3️⃣ 验证配置文件内容:")
    try:
        config_path = "config/environment_config.json"
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        models = config.get("models", {})
        search_settings = config.get("search_settings", {})
        analyse_settings = config.get("analyse_settings", {})
        
        print(f"   ✅ 默认模型: {models.get('default_model')}")
        print(f"   ✅ 查询数量: {search_settings.get('default_query_count')}")
        print(f"   ✅ URL总数: {search_settings.get('default_total_urls')}")
        print(f"   ✅ 爬虫数量: {search_settings.get('default_top_n')}")
        print(f"   ✅ 最大交互轮数: {analyse_settings.get('max_interaction_rounds')}")
        
        # 验证用户要求的设置
        expected_values = {
            "default_model": "gemini-2.5-flash",
            "default_query_count": 30,
            "default_total_urls": 200,
            "default_top_n": 70
        }
        
        print("\n4️⃣ 验证用户要求的设置:")
        all_correct = True
        for key, expected in expected_values.items():
            if key == "default_model":
                actual = models.get(key)
            else:
                actual = search_settings.get(key)
            
            if actual == expected:
                print(f"   ✅ {key}: {actual} (符合要求)")
            else:
                print(f"   ❌ {key}: {actual} (期望: {expected})")
                all_correct = False
        
        if all_correct:
            print("\n🎉 所有配置都符合用户要求！")
        else:
            print("\n⚠️ 部分配置不符合用户要求，需要调整")
            
    except Exception as e:
        print(f"   ❌ 配置文件验证失败: {e}")

if __name__ == "__main__":
    test_config_loading()
