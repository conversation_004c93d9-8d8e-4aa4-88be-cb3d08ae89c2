# LLM Search Host 使用指南

本指南详细介绍如何使用两种不同的Host实现：复杂版本和简化版本。

## 快速选择指南

### 什么时候使用简化版本 (SimpleLLMSearch)？

✅ **推荐使用简化版本的场景：**
- 你明确知道要执行什么搜索任务
- 搜索模式相对固定
- 对性能有较高要求
- 生产环境，追求稳定性
- 团队维护能力有限
- 资源受限的环境

### 什么时候使用复杂版本 (LLM_search)？

✅ **推荐使用复杂版本的场景：**
- 需要AI智能选择搜索策略
- 有多种搜索引擎可选择
- 需要处理复杂的多步骤任务
- 研究和实验性项目
- 对智能化有较高要求
- 需要根据不同场景自适应调整

## 使用方法

### 1. 简化版本使用示例

```python
from src.search.llm_search_simple_host import SimpleLLMSearch

# 创建实例
llm_search = SimpleLLMSearch(
    model="gemini-2.0-flash-thinking-exp-01-21",
    engine="google",
    each_query_result=10
)

# 生成搜索查询
queries = llm_search.get_queries(
    topic="机器学习优化",
    description="研究机器学习中的优化算法"
)
print(f"生成的查询: {queries}")

# 执行单个搜索
search_results = llm_search.web_search("深度学习优化算法")
print(f"搜索结果: {search_results}")

# 批量搜索
urls = llm_search.batch_web_search(
    queries=queries[:3],
    topic="机器学习优化",
    top_n=15
)
print(f"相关URLs: {urls}")

# 爬取网页内容
crawl_results = llm_search.crawl_urls(
    urls=urls[:5],
    topic="机器学习优化"
)
print(f"爬取结果: {crawl_results}")
```

### 2. 复杂版本使用示例

```python
from src.search.llm_search_host import LLM_search

# 创建实例
llm_search = LLM_search(
    model="gemini-2.0-flash-thinking-exp-01-21",
    engine="google",
    each_query_result=10
)

# 生成搜索查询（AI会智能分析任务）
queries = llm_search.get_queries(
    topic="量子计算",
    description="研究量子计算的最新进展和应用"
)
print(f"AI生成的查询: {queries}")

# 执行单个搜索（AI会选择最合适的搜索策略）
search_results = llm_search.web_search("量子计算算法")
print(f"搜索结果: {search_results}")

# 批量搜索（AI会优化搜索策略）
urls = llm_search.batch_web_search(
    queries=queries,
    topic="量子计算",
    top_n=20
)
print(f"相关URLs: {urls}")

# 爬取网页内容
crawl_results = llm_search.crawl_urls(
    urls=urls[:5],
    topic="量子计算"
)
print(f"爬取结果: {crawl_results}")
```

## 配置说明

### 环境变量配置

在使用之前，请确保设置了必要的API密钥：

```bash
# LLM API密钥（至少设置一个）
export OPENAI_API_KEY="your_openai_key"
export GOOGLE_API_KEY="your_google_key"

# 搜索引擎API密钥（至少设置一个）
export SERP_API_KEY="your_serp_key"
export BING_SEARCH_V7_SUBSCRIPTION_KEY="your_bing_key"
```

### 参数说明

```python
# 通用参数
model="gemini-2.0-flash-thinking-exp-01-21"  # LLM模型
infer_type="OpenAI"                          # 推理类型
engine="google"                              # 搜索引擎 (google/bing/baidu)
each_query_result=10                         # 每个查询返回的结果数
filter_date=None                             # 日期过滤器
max_workers=10                               # 最大并发数
```

## 性能对比测试

### 运行对比测试

```python
# 运行架构对比测试
python test_architecture_comparison.py
```

这个测试会：
1. 测试两种版本的功能
2. 比较执行时间
3. 分析性能差异
4. 提供选择建议

### 预期结果

```
=== 测试复杂版本 (Host → Agent → Client → Server) ===
✓ 复杂版本测试成功
  生成查询数: 6
  查询示例: ['机器学习优化算法', '深度学习优化技术']
  执行时间: 3.45秒
  架构路径: Host → Agent → Client → Server
  特点: 包含LLM智能决策，可以选择最合适的工具和策略

=== 测试简化版本 (Host → Client → Server) ===
✓ 简化版本测试成功
  生成查询数: 6
  查询示例: ['机器学习优化算法', '深度学习优化技术']
  执行时间: 2.12秒
  架构路径: Host → Client → Server
  特点: 直接调用MCP工具，无AI决策开销

⚡ 简化版本比复杂版本快 1.6x
```

## 错误处理

### 常见错误及解决方案

1. **API密钥错误**
```python
# 错误信息：No valid search engine key provided
# 解决方案：设置搜索引擎API密钥
export SERP_API_KEY="your_key"
```

2. **连接超时**
```python
# 错误信息：Failed to connect to MCP server
# 解决方案：检查网络连接，重试
```

3. **模型不可用**
```python
# 错误信息：Model not available
# 解决方案：检查LLM API密钥和模型名称
```

## 最佳实践

### 1. 选择合适的版本

```python
# 生产环境，固定搜索模式
from src.search.llm_search_simple_host import SimpleLLMSearch
llm_search = SimpleLLMSearch()

# 研究环境，需要智能决策
from src.search.llm_search_host import LLM_search
llm_search = LLM_search()
```

### 2. 批量处理优化

```python
# 推荐：批量处理多个查询
urls = llm_search.batch_web_search(queries, topic, top_n=20)

# 不推荐：逐个处理查询
# for query in queries:
#     result = llm_search.web_search(query)  # 效率低
```

### 3. 资源管理

```python
# 简化版本会自动清理连接
# 复杂版本也会自动清理连接
# 无需手动管理连接
```

### 4. 错误重试

```python
import time

def robust_search(llm_search, topic, max_retries=3):
    for i in range(max_retries):
        try:
            return llm_search.get_queries(topic)
        except Exception as e:
            if i == max_retries - 1:
                raise
            time.sleep(2 ** i)  # 指数退避
```

## 迁移指南

### 从原有LLM_search迁移

```python
# 原有代码
from LLM_search import LLM_search
llm_search = LLM_search()

# 迁移到简化版本（推荐用于生产环境）
from src.search.llm_search_simple_host import SimpleLLMSearch
llm_search = SimpleLLMSearch()

# 迁移到复杂版本（推荐用于研究环境）
from src.search.llm_search_host import LLM_search
llm_search = LLM_search()

# API接口完全兼容，无需修改其他代码
```

### 兼容性保证

两个版本都保持与原有LLM_search完全相同的API接口：
- `get_queries(topic, description="")`
- `web_search(query)`
- `batch_web_search(queries, topic, top_n=20)`
- `snippet_filter(topic, snippet)`
- `crawl_urls(urls, topic="")`

## 总结

- **简化版本**：适合生产环境，追求性能和稳定性
- **复杂版本**：适合研究环境，需要AI智能决策
- **API兼容**：两个版本都与原有接口完全兼容
- **自动管理**：连接和资源都会自动管理
- **灵活选择**：可以根据不同场景选择不同版本
