[project]
name = "llmxmapreduce-v2"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "asyncio-mqtt>=0.16.2",
    "certifi==2025.4.26",
    "crawl4ai==0.4.248",
    "fastapi>=0.115.12",
    "gevent>=25.5.1",
    "google-genai>=1.2.0",
    "hf-xet==1.1.2",
    "huggingface-hub==0.32.1",
    "mcp>=1.9.1",
    "nest-asyncio>=1.6.0",
    "nltk>=3.9.1",
    "numpy>=2.2.6",
    "openai>=1.82.0",
    "pandas>=2.2.3",
    "requests>=2.32.3",
    "tabulate>=0.9.0",
    "tenacity>=9.1.2",
    "transformers>=4.52.3",
    "trio>=0.30.0",
    "uvicorn>=0.34.2",
    "websockets>=14.2",
]

[[tool.uv.index]]
url = "https://pypi.tuna.tsinghua.edu.cn/simple"
default = true
