# MCP版本LLM搜索替换指南

## 概述

本指南说明如何将原有的`LLM_search`模块替换为基于MCP协议的版本，实现无缝升级。

## 文件结构

```
new/
├── src/
│   ├── search/
│   │   ├── llm_search_mcp_server.py      # MCP服务器实现
│   │   ├── llm_search_mcp_client.py      # MCP客户端实现
│   │   ├── llm_search_host.py            # LLM搜索主机（替代原有LLM_search）
│   │   └── llm_search_replacement.py     # 兼容性包装器
│   ├── LLM_search_mcp.py                 # 直接替换文件
│   └── start_pipeline_mcp.py             # 使用MCP的启动脚本
├── config/
│   └── llm_search_mcp_config.json        # MCP配置文件
├── test_mcp_integration.py               # 集成测试脚本
└── docs/
    ├── mcp_protocol_guide.md             # MCP协议详解
    └── mcp_replacement_guide.md          # 本文件
```

## 替换步骤

### 1. 环境准备

确保设置了必要的API密钥：

```bash
export OPENAI_API_KEY="your-openai-key"
export GOOGLE_API_KEY="your-google-key"
export SERP_API_KEY="your-serpapi-key"
export BING_SEARCH_V7_SUBSCRIPTION_KEY="your-bing-key"
```

### 2. 安装依赖

```bash
pip install mcp
```

### 3. 替换方式

#### 方式1: 直接替换导入（推荐）

将原有代码中的：
```python
from src.LLM_search import LLM_search
```

替换为：
```python
from src.search.llm_search_host import LLM_search
```

#### 方式2: 使用兼容性包装器

将原有的`LLM_search.py`文件备份，然后将`LLM_search_mcp.py`重命名为`LLM_search.py`：

```bash
# 备份原文件
mv src/LLM_search.py src/LLM_search_original.py

# 使用MCP版本
cp src/LLM_search_mcp.py src/LLM_search.py
```

#### 方式3: 使用新的启动脚本

直接使用`start_pipeline_mcp.py`替代原有的`start_pipeline.py`：

```bash
python src/start_pipeline_mcp.py --topic "your topic" --description "your description" --top_n 10
```

## 接口兼容性

### 原有接口
```python
class LLM_search:
    def __init__(self, model, infer_type, engine, each_query_result, ...):
        pass
    
    def get_queries(self, topic: str, description: str = "") -> List[str]:
        pass
    
    def web_search(self, query: str) -> dict:
        pass
    
    def batch_web_search(self, queries: List[str], topic: str, top_n: int) -> List[str]:
        pass
    
    def snippet_filter(self, topic: str, snippet: str) -> float:
        pass
```

### MCP版本接口
```python
class LLM_search:
    def __init__(self, model, infer_type, engine, each_query_result, ...):
        # 完全相同的参数
        pass
    
    def get_queries(self, topic: str, description: str = "") -> List[str]:
        # 通过MCP调用，返回相同格式
        pass
    
    def web_search(self, query: str) -> dict:
        # 通过MCP调用，返回相同格式
        pass
    
    def batch_web_search(self, queries: List[str], topic: str, top_n: int) -> List[str]:
        # 通过MCP调用，返回相同格式
        pass
    
    def snippet_filter(self, topic: str, snippet: str) -> float:
        # 保持相同的接口
        pass
```

## 测试验证

### 1. 运行集成测试
```bash
python test_mcp_integration.py
```

### 2. 测试MCP服务器
```bash
python test_mcp_llm_search.py
```

### 3. 测试新的启动脚本
```bash
python src/start_pipeline_mcp.py --test
```

## 性能对比

| 特性 | 原版本 | MCP版本 |
|------|--------|---------|
| 查询生成 | 直接调用 | 通过MCP调用 |
| 网络搜索 | 直接调用 | 通过MCP调用 |
| 并发处理 | 线程池 | 异步+MCP |
| 错误处理 | 基础异常 | 增强的错误处理 |
| 可扩展性 | 单体架构 | 微服务架构 |
| 监控能力 | 基础日志 | 详细的MCP日志 |

## 优势

### 1. 标准化
- 遵循MCP协议标准
- 易于与其他系统集成
- 支持工具发现和动态调用

### 2. 可扩展性
- 服务器和客户端分离
- 支持多个客户端连接
- 易于添加新功能

### 3. 可维护性
- 清晰的模块分离
- 标准化的错误处理
- 详细的日志记录

### 4. 性能
- 异步处理
- 连接复用
- 智能缓存

## 故障排除

### 1. 连接问题
```bash
# 检查MCP服务器是否运行
python -m src.search.llm_search_mcp_server

# 检查环境变量
echo $OPENAI_API_KEY
echo $SERP_API_KEY
```

### 2. API密钥问题
```bash
# 验证API密钥
python -c "
import os
print('OpenAI:', bool(os.getenv('OPENAI_API_KEY')))
print('Google:', bool(os.getenv('GOOGLE_API_KEY')))
print('SERP:', bool(os.getenv('SERP_API_KEY')))
print('Bing:', bool(os.getenv('BING_SEARCH_V7_SUBSCRIPTION_KEY')))
"
```

### 3. 导入问题
```bash
# 检查Python路径
python -c "
import sys
print('Python path:')
for p in sys.path:
    print(f'  {p}')
"
```

## 回滚方案

如果需要回滚到原版本：

```bash
# 恢复原文件
mv src/LLM_search_original.py src/LLM_search.py

# 或者直接使用原有的start_pipeline.py
python LLMxMapReduce_V2/src/start_pipeline.py --topic "your topic"
```

## 配置选项

### MCP服务器配置
```json
{
  "servers": {
    "llm_search_server": {
      "command": "python",
      "args": ["-m", "src.search.llm_search_mcp_server"],
      "env": {
        "PYTHONPATH": ".",
        "OPENAI_API_KEY": "${OPENAI_API_KEY}",
        "GOOGLE_API_KEY": "${GOOGLE_API_KEY}",
        "SERP_API_KEY": "${SERP_API_KEY}",
        "BING_SEARCH_V7_SUBSCRIPTION_KEY": "${BING_SEARCH_V7_SUBSCRIPTION_KEY}"
      }
    }
  }
}
```

### 客户端配置
```python
# 自定义MCP客户端配置
server_config = {
    "command": "python",
    "args": ["-m", "src.search.llm_search_mcp_server"],
    "env": {
        "PYTHONPATH": ".",
        # 添加自定义环境变量
    }
}

client = LLMSearchMCPClient(server_config)
```

## 最佳实践

1. **渐进式替换**: 先在测试环境验证，再在生产环境部署
2. **监控日志**: 密切关注MCP通信日志
3. **性能测试**: 对比替换前后的性能指标
4. **备份数据**: 保留原有实现作为备份
5. **文档更新**: 更新相关文档和使用说明

## 支持

如果遇到问题，请：
1. 查看日志文件
2. 运行测试脚本
3. 检查API密钥配置
4. 验证网络连接
5. 参考MCP协议文档
