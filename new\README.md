# MapxReduceV3 - 新版本管道实现

这是MapxReduceV3项目的新版本实现，提供了从用户任务到综述生成的完整流程。

## 项目结构

```
new/
├── pipeline.py              # 主管道模块
├── src/
│   ├── args.py             # 命令行参数解析
│   ├── exceptions.py       # 自定义异常类
│   ├── data_structure/     # 数据结构定义
│   │   ├── survey.py       # Survey类定义
│   │   ├── content.py      # 内容相关类
│   │   ├── digest.py       # 摘要相关类
│   │   └── ...
│   ├── search/             # 文献搜索模块
│   │   ├── analyse.py      # 文献分析接口
│   │   └── web_search.py   # 网络搜索
│   ├── encode/             # 编码处理模块
│   │   ├── encode_pipeline.py  # 编码管道
│   │   └── group.py        # 论文分组
│   └── utils/              # 工具函数
│       ├── process_str.py  # 字符串处理
│       └── watch_data.py   # 数据监控
├── test/                   # 测试数据目录
├── config/                 # 配置文件
└── test_pipeline.py        # 测试脚本
```

## 核心功能

### 1. 主管道流程 (pipeline.py)

主管道协调整个综述生成流程：

1. **任务分析** - 调用`analyse`接口分析用户任务并检索相关文献
2. **文献编码** - 使用`encode_pipeline`将文献转换为Survey对象
3. **结果保存** - 将生成的Survey保存为JSON格式

### 2. 文献分析 (src/search/analyse.py)

提供文献分析和检索的接口：

- `analyse(task, description, top_n)` - 分析任务并检索文献
- 将检索结果保存到`test/{task_name}/`目录下
- 每篇文献保存为单独的JSON文件

### 3. 编码管道 (src/encode/encode_pipeline.py)

负责文献数据的加载和处理：

- `encode_pipeline(task_dir, args, task_name)` - 完整编码流程
- 从目录加载文献数据
- 创建Survey对象
- 执行论文分组

### 4. 论文分组 (src/encode/group.py)

支持多种分组策略：

- **随机分组** - 随机将论文分成若干组
- **LLM分组** - 基于语义的智能分组（接口定义）
- **关键词分组** - 基于关键词的简单分组

## 使用方法

### 1. 简单使用

```python
from pipeline import run_pipeline_simple

# 运行完整管道
survey = run_pipeline_simple(
    task="machine learning optimization",
    description="Research on optimization techniques in ML",
    top_n=10,
    output_file="output/survey.json"
)

print(f"Generated survey: {survey.title}")
print(f"Papers processed: {len(survey.papers)}")
```

### 2. 命令行使用

```bash
python pipeline.py --topic "machine learning" --description "ML research" --top_n 10 --output_file "output.json"
```

### 3. 分步使用

```python
from src.search.analyse import analyse
from src.encode.encode_pipeline import encode_pipeline

# 步骤1: 分析任务并检索文献
literature_dir = analyse("machine learning", "ML research", top_n=10)

# 步骤2: 编码文献生成Survey
survey = encode_pipeline(literature_dir, task_name="machine_learning")
```

## 配置参数

主要配置参数（通过args传递）：

- `topic` - 研究主题
- `description` - 主题描述
- `top_n` - 检索文献数量
- `digest_group_mode` - 分组模式 ('random' 或 'llm')
- `skeleton_group_size` - 每组论文数量
- `output_file` - 输出文件路径

## 测试

运行测试脚本：

```bash
python new/test_pipeline.py
```

测试包括：
1. 各个组件的单独测试
2. 完整管道流程测试

## 注意事项

1. **接口定义** - 当前实现主要提供接口定义，具体的LLM调用、网络搜索等功能需要进一步实现
2. **数据格式** - 文献数据采用JSON格式，包含title、abstract、txt等字段
3. **目录结构** - 文献保存在`test/{task_name}/`目录下
4. **依赖关系** - 确保相关的数据结构类（Survey、Digest等）已正确实现

## 扩展开发

要扩展功能，可以：

1. 实现具体的网络搜索逻辑（在`analyse.py`中）
2. 添加更多的论文分组策略（在`group.py`中）
3. 集成LLM服务进行智能分析
4. 添加更多的数据处理和优化功能

## 错误处理

项目定义了多种自定义异常类（在`exceptions.py`中）：

- `AnalyseError` - 文献分析异常
- `EncodeError` - 编码处理异常
- `PipelineError` - 管道流程异常
- `GroupingError` - 论文分组异常

所有异常都继承自`MapxReduceException`基类。
