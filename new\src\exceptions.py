"""
自定义异常类模块
定义项目中使用的各种异常类型
"""


class MapxReduceException(Exception):
    """
    MapxReduce项目的基础异常类
    """
    pass


class MdNotFoundError(MapxReduceException):
    """
    Markdown内容未找到异常
    """
    def __init__(self, raw_content="", message="Markdown content not found"):
        self.raw_content = raw_content
        self.message = message
        super().__init__(self.message)
    
    def __str__(self):
        return f"{self.message}. Raw content: {self.raw_content[:100]}..."


class BibkeyNotFoundError(MapxReduceException):
    """
    引用键未找到异常
    """
    def __init__(self, bibkeys=None, content="", legal_bibkeys=None, 
                 message="Bibliography key not found"):
        self.bibkeys = bibkeys or []
        self.content = content
        self.legal_bibkeys = legal_bibkeys or []
        self.message = message
        super().__init__(self.message)
    
    def __str__(self):
        return (f"{self.message}. "
                f"Missing bibkeys: {self.bibkeys}, "
                f"Legal bibkeys: {self.legal_bibkeys}")


class AnalyseError(MapxReduceException):
    """
    文献分析异常
    """
    pass


class EncodeError(MapxReduceException):
    """
    编码处理异常
    """
    pass


class PipelineError(MapxReduceException):
    """
    管道流程异常
    """
    pass


class SurveyError(MapxReduceException):
    """
    Survey对象相关异常
    """
    pass


class GroupingError(MapxReduceException):
    """
    论文分组异常
    """
    pass
