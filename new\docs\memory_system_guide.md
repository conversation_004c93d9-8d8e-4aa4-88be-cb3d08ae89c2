# LLM Search 对话历史存储系统指南

## 概述

LLM Search 框架现在集成了完整的对话历史存储系统，能够自动保存、管理和检索不同LLM模型的对话历史，支持上下文连续性和智能对话管理。

## 核心特性

### 🧠 智能内存管理
- **按模型分离存储**：不同LLM模型的对话历史完全隔离
- **自动上下文加载**：新对话自动加载相关历史上下文
- **配置化管理**：通过配置文件灵活控制内存行为
- **持久化存储**：对话历史自动保存到JSON文件

### 🔧 高级功能
- **文件大小控制**：自动清理旧对话，防止文件过大
- **备份机制**：自动创建备份，防止数据丢失
- **性能优化**：支持异步保存和批量操作
- **统计分析**：提供详细的对话历史统计信息

## 快速开始

### 1. 基本使用

```python
from src.search.llm_search_host import LLM_search

# 创建启用内存的LLM_search实例
llm_search = LLM_search(
    model="gemini-2.0-flash-thinking-exp-01-21",
    use_memory=True,
    max_context_messages=10
)

# 正常使用，对话历史会自动保存
queries = llm_search.get_queries("机器学习", "深度学习相关内容")
```

### 2. 内存管理

```python
# 查看对话历史
history = llm_search.get_conversation_history(limit=5)
print(f"最近5条对话: {len(history)}")

# 获取统计信息
stats = llm_search.get_memory_statistics()
print(f"总对话数: {stats}")

# 导出对话历史
llm_search.export_conversation_history("my_conversations.json")

# 清除对话历史
llm_search.clear_conversation_history()
```

### 3. 配置管理

```python
# 动态控制内存功能
llm_search.set_memory_enabled(False)  # 禁用
llm_search.set_memory_enabled(True)   # 启用
```

## 配置文件详解

### memory_config.json 结构

```json
{
  "memory_settings": {
    "enabled": true,
    "memory_file": "memory.json",
    "max_history_per_model": 1000,
    "max_context_messages": 10,
    "auto_backup": true
  },
  "model_specific_settings": {
    "gemini-2.0-flash-thinking-exp-01-21": {
      "max_context_messages": 12,
      "max_history": 1500
    }
  },
  "storage_optimization": {
    "max_file_size_mb": 100,
    "auto_cleanup_enabled": true,
    "cleanup_age_days": 30
  }
}
```

### 配置项说明

#### memory_settings（基本设置）
- `enabled`: 是否启用内存功能
- `memory_file`: 内存文件路径
- `max_history_per_model`: 每个模型最大历史记录数
- `max_context_messages`: 默认最大上下文消息数
- `auto_backup`: 是否自动备份

#### model_specific_settings（模型特定设置）
为不同模型设置专门的参数：
- `max_context_messages`: 该模型的最大上下文消息数
- `max_history`: 该模型的最大历史记录数

#### storage_optimization（存储优化）
- `max_file_size_mb`: 内存文件最大大小（MB）
- `auto_cleanup_enabled`: 是否自动清理旧记录
- `cleanup_age_days`: 清理多少天前的记录

## 架构设计

### 组件关系

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   LLM_search    │───▶│  RequestWrapper  │───▶│ MemoryManager   │
│                 │    │                  │    │                 │
│ - get_queries() │    │ - completion()   │    │ - add_conv()    │
│ - batch_search()│    │ - use_memory     │    │ - get_context() │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌──────────────────┐    ┌─────────────────┐
                       │   LLM API        │    │  memory.json    │
                       │ (OpenAI/Google)  │    │   (持久化)      │
                       └──────────────────┘    └─────────────────┘
```

### 数据流程

1. **对话发起**：用户调用 `get_queries()` 或其他LLM方法
2. **上下文加载**：RequestWrapper 自动加载历史上下文
3. **LLM调用**：将当前消息+历史上下文发送给LLM
4. **响应处理**：获取LLM响应
5. **历史保存**：MemoryManager 保存对话到内存和文件
6. **自动优化**：根据配置进行文件大小控制和清理

## 内存文件格式

### memory.json 结构

```json
{
  "gemini-2.0-flash-thinking-exp-01-21": [
    {
      "timestamp": "2024-01-15T10:30:00.123456",
      "messages": [
        {
          "role": "user",
          "content": "什么是机器学习？"
        }
      ],
      "response": "机器学习是人工智能的一个分支...",
      "metadata": {
        "token_usage": {"input": 20, "output": 150},
        "model": "gemini-2.0-flash-thinking-exp-01-21",
        "call_count": 1
      }
    }
  ],
  "gpt-4": [
    // GPT-4的对话历史...
  ]
}
```

### 字段说明

- `timestamp`: 对话时间戳（ISO格式）
- `messages`: 用户输入消息列表
- `response`: LLM响应内容
- `metadata`: 元数据（token使用量、模型信息等）

## 高级用法

### 1. 自定义内存管理器

```python
from src.utils.memory_manager import MemoryManager

# 创建自定义内存管理器
memory_manager = MemoryManager(
    memory_file="custom_memory.json",
    max_history_per_model=500,
    config_file="custom_config.json"
)

# 手动添加对话
memory_manager.add_conversation(
    model="my-model",
    messages=[{"role": "user", "content": "Hello"}],
    response="Hi there!",
    metadata={"custom_field": "value"}
)
```

### 2. 批量操作

```python
# 获取所有模型的统计信息
stats = memory_manager.get_statistics()
for model, model_stats in stats.items():
    print(f"{model}: {model_stats['total_conversations']} conversations")

# 批量导出
for model in memory_manager.get_all_models():
    memory_manager.export_history(model, f"{model}_history.json")
```

### 3. 上下文策略

```python
# 获取智能上下文（自动使用配置中的最大消息数）
context = memory_manager.get_recent_context("my-model")

# 获取指定数量的上下文
context = memory_manager.get_recent_context("my-model", max_messages=5)
```

## 性能优化建议

### 1. 配置优化
- 根据使用场景调整 `max_history_per_model`
- 启用 `auto_cleanup_enabled` 防止文件过大
- 合理设置 `max_context_messages` 平衡性能和效果

### 2. 存储优化
- 定期备份重要的对话历史
- 监控内存文件大小，及时清理
- 考虑使用数据库存储大量历史数据

### 3. 使用建议
- 在生产环境中禁用不必要的详细日志
- 定期检查和更新配置文件
- 监控内存使用情况和性能指标

## 故障排除

### 常见问题

1. **内存文件损坏**
   - 检查 `.backup` 文件
   - 验证JSON格式
   - 重新初始化内存管理器

2. **性能问题**
   - 减少 `max_context_messages`
   - 启用自动清理
   - 检查文件大小

3. **配置问题**
   - 验证配置文件格式
   - 检查文件权限
   - 查看日志错误信息

### 调试技巧

```python
import logging
logging.basicConfig(level=logging.DEBUG)

# 启用详细日志
logger = logging.getLogger('src.utils.memory_manager')
logger.setLevel(logging.DEBUG)
```

## 最佳实践

1. **定期备份**：重要项目应定期备份内存文件
2. **配置管理**：使用版本控制管理配置文件
3. **监控使用**：定期检查内存使用统计
4. **测试验证**：在生产环境前充分测试内存功能
5. **文档更新**：记录自定义配置和使用模式

## 扩展开发

### 添加新功能

1. **自定义存储后端**：实现数据库存储
2. **压缩算法**：添加对话内容压缩
3. **搜索功能**：实现历史对话搜索
4. **分析工具**：开发对话分析和可视化

### API扩展

```python
class CustomMemoryManager(MemoryManager):
    def search_conversations(self, query: str, model: str = None):
        """搜索对话历史"""
        # 实现搜索逻辑
        pass
    
    def analyze_patterns(self, model: str):
        """分析对话模式"""
        # 实现分析逻辑
        pass
```

这个对话历史系统为LLM Search框架提供了强大的记忆能力，使得AI助手能够在多轮对话中保持上下文连续性，提供更智能和个性化的服务。
