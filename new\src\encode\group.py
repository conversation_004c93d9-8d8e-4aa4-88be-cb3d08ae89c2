"""
论文分组模块
将文献物理分组到不同子文件夹中
"""

import json
import logging
import random
import shutil
from pathlib import Path
from typing import List, Dict, Any

logger = logging.getLogger(__name__)


def group_papers(args):
    """
    将文献分组到子文件夹中

    Args:
        args: 参数对象，包含task名称、文件夹地址等信息
    """
    task_name = getattr(args, 'topic', 'default_task')
    task_dir = Path(getattr(args, 'task_dir', f'new/test/{task_name}'))
    group_mode = getattr(args, 'digest_group_mode', 'random')
    group_size = getattr(args, 'skeleton_group_size', 3)

    logger.info(f"Grouping papers in {task_dir} using {group_mode} mode")

    if not task_dir.exists():
        raise FileNotFoundError(f"Task directory not found: {task_dir}")

    # 获取所有论文文件
    paper_files = [f for f in task_dir.glob("*.json") if f.name != "summary.json"]

    if not paper_files:
        logger.warning("No paper files found to group")
        return

    # 根据模式进行分组
    if group_mode == 'random':
        groups = _random_grouping(paper_files, group_size)
    elif group_mode == 'llm':
        groups = _llm_grouping(paper_files, task_dir, group_size)
    else:
        logger.warning(f"Unknown grouping mode: {group_mode}, using random")
        groups = _random_grouping(paper_files, group_size)

    # 创建子文件夹并移动文件
    for i, group_files in enumerate(groups):
        group_dir = task_dir / f"组{chr(65+i)}"  # 组A, 组B, 组C...
        group_dir.mkdir(exist_ok=True)

        for paper_file in group_files:
            dest_file = group_dir / paper_file.name
            shutil.copy2(paper_file, dest_file)

        logger.info(f"Created group {group_dir.name} with {len(group_files)} papers")

    logger.info(f"Paper grouping completed: {len(groups)} groups created")


def _random_grouping(paper_files: List[Path], group_size: int) -> List[List[Path]]:
    """随机分组"""
    files = paper_files.copy()
    random.shuffle(files)

    groups = []
    for i in range(0, len(files), group_size):
        group = files[i:i + group_size]
        groups.append(group)

    return groups


def _llm_grouping(paper_files: List[Path], task_dir: Path, group_size: int) -> List[List[Path]]:
    """基于LLM的智能分组（简化实现）"""
    # 读取论文内容进行关键词分组
    keyword_groups = {}

    for paper_file in paper_files:
        try:
            with open(paper_file, 'r', encoding='utf-8') as f:
                paper_data = json.load(f)

            # 提取关键词
            keywords = paper_data.get('keywords', [])
            if not keywords:
                title_words = paper_data.get('title', '').lower().split()
                keywords = [word for word in title_words if len(word) > 3][:3]

            # 使用第一个关键词作为分组依据
            primary_keyword = keywords[0] if keywords else 'general'

            if primary_keyword not in keyword_groups:
                keyword_groups[primary_keyword] = []
            keyword_groups[primary_keyword].append(paper_file)

        except Exception as e:
            logger.warning(f"Error processing {paper_file}: {e}")
            # 默认分到general组
            if 'general' not in keyword_groups:
                keyword_groups['general'] = []
            keyword_groups['general'].append(paper_file)

    # 合并小组并确保每组大小合适
    final_groups = []
    current_group = []

    for keyword, files in keyword_groups.items():
        if len(files) >= group_size:
            final_groups.append(files)
        else:
            current_group.extend(files)
            if len(current_group) >= group_size:
                final_groups.append(current_group)
                current_group = []

    # 处理剩余文件
    if current_group:
        final_groups.append(current_group)

async def create_session(tool_cfg):
    from mcp import ClientSession
    from mcp.client.stdio import stdio_client
    params = {
        "command": tool_cfg["command"],
        "args": tool_cfg.get("args", []),
        "env": tool_cfg.get("env", None)
    }
    read, write = await stdio_client(**params).__aenter__()
    session = await ClientSession(read, write).__aenter__()
    await session.initialize()
    return session