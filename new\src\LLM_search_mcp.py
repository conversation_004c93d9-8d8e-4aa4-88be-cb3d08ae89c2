#!/usr/bin/env python3
"""
LLM搜索MCP兼容性包装器
直接替换原有的LLM_search.py，提供完全相同的接口
"""

# 导入MCP版本的LLM搜索
from search.llm_search_host import LLM_search, create_llm_search

# 导入必要的异常类
try:
    from exceptions import QueryParseError
except ImportError:
    class QueryParseError(Exception):
        """查询解析错误"""
        pass

# 为了完全兼容，重新导出所有内容
__all__ = ['LLM_search', 'create_llm_search', 'QueryParseError']

# 如果需要，也可以提供一些兼容性函数
def get_llm_search_instance(*args, **kwargs):
    """获取LLM搜索实例的兼容性函数"""
    return LLM_search(*args, **kwargs)
