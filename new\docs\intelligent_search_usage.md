# 智能LLM搜索系统使用指南

## 概述

智能LLM搜索系统实现了您要求的架构：**Pipeline调用LLM_search → 启动LLM对话程序 → LLM选择MCP工具 → 执行原始搜索操作**。

## 架构特点

### 🎯 **核心流程**
1. **Pipeline调用** - 保持原有`LLM_search`接口不变
2. **LLM对话** - 智能分析搜索任务，选择最佳工具
3. **MCP工具调用** - 通过MCP协议调用搜索服务
4. **原始逻辑执行** - 执行MapxReduceV2原始搜索逻辑
5. **结果返回** - 保持与原接口完全一致的返回格式

### 🔧 **扩展性设计**
- 支持动态添加新的搜索引擎MCP服务器
- LLM可以智能选择最适合的搜索工具
- 模块化架构，易于维护和扩展

## 文件结构

```
new/src/search/
├── llm_conversation_agent.py      # LLM对话代理
├── intelligent_llm_search.py      # 智能LLM搜索主机
├── llm_search_mcp_server.py       # 原始搜索功能的MCP服务器
├── mcp_client.py                  # 纯粹的MCP通信客户端
└── ...

new/test_intelligent_search_flow.py  # 完整流程测试
new/docs/intelligent_search_usage.md # 本文档
```

## 使用方式

### 1. 直接替换原有LLM_search

```python
# 原有代码
from src.LLM_search import LLM_search

# 替换为智能版本
from src.search.intelligent_llm_search import LLM_search

# 其他代码保持完全不变！
retriever = LLM_search(model='gemini-2.0-flash-thinking-exp-01-21', ...)
queries = retriever.get_queries(topic=args.topic, description=args.description)
url_list = retriever.batch_web_search(queries=queries, topic=args.topic, top_n=args.top_n)
```

### 2. 在start_pipeline.py中使用

```python
# 在start_pipeline.py中，只需要修改导入
from src.search.intelligent_llm_search import LLM_search

# 其他代码保持不变
def main():
    args = parse_args()
    if args.topic:
        # 这里会自动启动LLM对话，选择最佳搜索工具
        retriever = LLM_search(model='gemini-2.0-flash-thinking-exp-01-21', ...)
        queries = retriever.get_queries(topic=args.topic, description=args.description)
        url_list = retriever.batch_web_search(queries=queries, topic=args.topic, top_n=args.top_n)
        # ... 后续流程保持不变
```

## 工作原理

### 1. LLM对话代理 (`llm_conversation_agent.py`)

**职责**：
- 接收搜索任务描述
- 与LLM对话，分析任务需求
- 让LLM选择最合适的搜索工具
- 调用选定的MCP工具
- 返回搜索结果

**核心方法**：
```python
async def execute_search_task(self, task_description: str) -> Dict[str, Any]:
    # 1. 构建LLM对话消息
    messages = self._create_conversation_messages(task_description)
    
    # 2. LLM分析并选择工具
    response = await self.request_wrapper.async_request(messages=messages)
    
    # 3. 解析LLM决策
    llm_decision = json.loads(response)
    
    # 4. 调用选定的MCP工具
    result = await self._call_mcp_tool(
        server_name=llm_decision["tool_call"]["server"],
        tool_name=llm_decision["tool_call"]["tool"],
        arguments=llm_decision["tool_call"]["arguments"]
    )
    
    return result
```

### 2. 智能LLM搜索 (`intelligent_llm_search.py`)

**职责**：
- 保持与原有`LLM_search`完全相同的接口
- 将方法调用转换为搜索任务描述
- 调用LLM对话代理执行任务
- 转换结果格式以保持兼容性

**接口兼容性**：
```python
class LLM_search:
    def get_queries(self, topic: str, description: str = "") -> List[str]:
        # 转换为任务描述
        task_description = f"生成搜索查询任务：主题: {topic}, 描述: {description}"
        
        # 调用LLM对话代理
        result = await self.conversation_agent.execute_search_task(task_description)
        
        # 返回与原接口一致的格式
        return result["tool_result"]["queries"]
```

### 3. MCP服务器 (`llm_search_mcp_server.py`)

**职责**：
- 提供原始MapxReduceV2搜索功能的MCP工具
- 注册`generate_search_queries`、`web_search`等工具
- 执行具体的搜索逻辑

## 扩展新的搜索引擎

### 1. 创建新的MCP服务器

```python
# 例如：academic_search_mcp_server.py
class AcademicSearchMCPServer:
    def __init__(self):
        self.server = Server("academic-search-server")
        self._register_tools()
    
    def _register_tools(self):
        @self.server.list_tools()
        async def handle_list_tools() -> List[Tool]:
            return [
                Tool(
                    name="academic_search",
                    description="Search academic papers and publications",
                    inputSchema={...}
                )
            ]
```

### 2. 注册到对话代理

```python
from src.search.intelligent_llm_search import LLM_search

retriever = LLM_search()

# 添加新的搜索引擎
retriever.add_search_engine("academic_search_mcp", {
    "command": "python",
    "args": ["-m", "src.search.academic_search_mcp_server"],
    "env": {"PYTHONPATH": "."},
    "description": "学术搜索引擎，专门用于学术论文搜索"
})

# LLM现在可以选择使用学术搜索引擎
```

### 3. LLM自动选择

LLM会根据任务需求自动选择最合适的搜索引擎：

```
用户任务: "搜索机器学习相关的学术论文"
LLM分析: "这是学术搜索任务，应该使用academic_search_mcp"
工具选择: academic_search_mcp -> academic_search
```

## 配置要求

### 环境变量
```bash
# LLM API密钥（至少设置一个）
export OPENAI_API_KEY="your-openai-key"
export GOOGLE_API_KEY="your-google-key"

# 搜索引擎API密钥（至少设置一个）
export SERP_API_KEY="your-serpapi-key"
export BING_SEARCH_V7_SUBSCRIPTION_KEY="your-bing-key"
```

### 依赖安装
```bash
pip install mcp
# 其他依赖已在项目中
```

## 测试验证

### 运行完整测试
```bash
python test_intelligent_search_flow.py
```

### 测试覆盖
- ✅ LLM对话代理功能
- ✅ 智能LLM搜索接口兼容性
- ✅ 原有接口完全兼容
- ✅ 搜索引擎扩展性
- ✅ 端到端流水线模拟

## 优势特性

### 1. 完全兼容
- 与原有`LLM_search`接口100%兼容
- 无需修改Pipeline中的任何代码
- 保持相同的返回格式和行为

### 2. 智能选择
- LLM根据任务需求智能选择搜索工具
- 支持多种搜索策略和引擎
- 可以处理复杂的搜索需求

### 3. 高度扩展
- 轻松添加新的搜索引擎
- 模块化架构，易于维护
- 支持不同类型的搜索服务

### 4. 标准化
- 基于MCP协议的标准化架构
- 清晰的职责分离
- 易于集成和调试

## 故障排除

### 1. LLM对话失败
```bash
# 检查LLM API密钥
echo $OPENAI_API_KEY
echo $GOOGLE_API_KEY
```

### 2. MCP连接问题
```bash
# 测试MCP服务器
python -m src.search.llm_search_mcp_server
```

### 3. 搜索API问题
```bash
# 检查搜索API密钥
echo $SERP_API_KEY
echo $BING_SEARCH_V7_SUBSCRIPTION_KEY
```

## 总结

这个智能LLM搜索系统完美实现了您的需求：

1. **保持接口兼容** - Pipeline代码无需修改
2. **LLM智能选择** - 根据任务自动选择最佳工具
3. **MCP标准化** - 基于标准协议的模块化架构
4. **高度扩展** - 轻松添加新的搜索引擎
5. **原始逻辑复用** - 完全复用MapxReduceV2的搜索逻辑

通过这个架构，您可以在保持原有系统稳定性的同时，获得智能化和可扩展的搜索能力。
