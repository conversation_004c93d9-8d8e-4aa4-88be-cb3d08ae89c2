import asyncio
import json
import logging
import os
import sys
from typing import List, Dict, Any, Optional
from datetime import datetime

from .llm_search_mcp_client import MCPClient, create_mcp_client_from_config

try:
    from ..request.wrapper import RequestWrapper
except ImportError:
    import sys
    import os
    sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
    from request.wrapper import RequestWrapper

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def load_model_config():
    """加载模型配置"""
    config_path = os.path.join(os.path.dirname(__file__), '..', '..', 'config', 'model_config.json')
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        logger.info(f"Model config loaded from: {config_path}")
        return config
    except Exception as e:
        logger.warning(f"Failed to load model config, using defaults: {e}")
        return {
            "search": {
                "host_llm": {
                    "model": "gemini-2.0-flash-thinking-exp-01-21",
                    "infer_type": "OpenAI"
                }
            }
        }

MODEL_CONFIG = load_model_config()


class ToolResultExtractor:
    """从工具调用结果中提取关键数据"""
    
    def extract(self, tool_name: str, result: Any) -> Dict[str, Any]:
        """根据工具类型提取关键数据"""
        if tool_name == "generate_search_queries":
            return self.extract_from_generate_search_queries(result)
        elif tool_name == "web_search":
            return self.extract_from_web_search(result)
        elif tool_name == "crawl_urls":
            return self.extract_from_crawl_urls(result)
        elif tool_name == "analyze_search_results":
            return self.extract_from_analyze_search_results(result)
        else:
            return {}
    
    def extract_from_generate_search_queries(self, result: Any) -> Dict[str, Any]:
        """从查询生成结果中提取查询列表"""
        if isinstance(result, dict) and "queries" in result:
            return {"queries": result["queries"]}
        elif isinstance(result, list):
            return {"queries": result}
        return {}
    
    def extract_from_web_search(self, result: Any) -> Dict[str, Any]:
        """从网络搜索结果中提取URLs"""
        urls = []
        if isinstance(result, dict):
            # 处理多查询结果格式
            if "results" in result:
                for query_result in result["results"]:
                    if isinstance(query_result, dict) and "results" in query_result:
                        for item in query_result["results"]:
                            if isinstance(item, dict) and "url" in item:
                                urls.append(item["url"])
            # 处理单查询结果格式
            elif "url" in result:
                urls.append(result["url"])
        elif isinstance(result, list):
            for item in result:
                if isinstance(item, dict) and "url" in item:
                    urls.append(item["url"])
        
        return {"urls": list(set(urls))} if urls else {}
    
    def extract_from_crawl_urls(self, result: Any) -> Dict[str, Any]:
        """从URL爬取结果中提取内容"""
        if isinstance(result, dict):
            crawled_content = result.get("results", [])
            successful_urls = [item.get("url") for item in crawled_content if isinstance(item, dict) and "url" in item]
            return {
                "crawled_content": crawled_content,
                "successful_urls": successful_urls
            }
        return {}
    
    def extract_from_analyze_search_results(self, result: Any) -> Dict[str, Any]:
        """从分析结果中提取关键信息"""
        return {"analysis": result}


class ParameterFiller:
    """智能填充工具调用参数"""
    
    def fill_params(self, tool_name: str, args: Dict[str, Any], extracted_data: Dict[str, Any]) -> Dict[str, Any]:
        """根据工具类型和历史数据智能填充参数"""
        filled_args = args.copy()
        
        if tool_name == "web_search":
            filled_args = self.fill_web_search_params(filled_args, extracted_data)
        elif tool_name == "crawl_urls":
            filled_args = self.fill_crawl_urls_params(filled_args, extracted_data)
        elif tool_name == "analyze_search_results":
            filled_args = self.fill_analyze_params(filled_args, extracted_data)
        
        return filled_args
    
    def fill_web_search_params(self, args: Dict[str, Any], extracted_data: Dict[str, Any]) -> Dict[str, Any]:
        """填充网络搜索参数"""
        # 如果没有指定queries，使用之前生成的queries
        if not args.get("queries") and extracted_data.get("search_queries"):
            args["queries"] = extracted_data["search_queries"]
            logger.info(f"Auto-filled web_search queries: {len(args['queries'])} queries")
        
        # 设置默认参数
        if "top_n" not in args:
            args["top_n"] = 5
        if "engine" not in args:
            args["engine"] = "google"
            
        return args
    
    def fill_crawl_urls_params(self, args: Dict[str, Any], extracted_data: Dict[str, Any]) -> Dict[str, Any]:
        """填充URL爬取参数"""
        # 如果没有指定url_list，使用之前搜索的URLs
        if not args.get("url_list") and extracted_data.get("urls"):
            args["url_list"] = extracted_data["urls"]
            logger.info(f"Auto-filled crawl_urls url_list: {len(args['url_list'])} URLs")
        
        # 设置默认参数
        if "top_n" not in args:
            args["top_n"] = 10
            
        return args
    
    def fill_analyze_params(self, args: Dict[str, Any], extracted_data: Dict[str, Any]) -> Dict[str, Any]:
        """填充分析参数"""
        # 如果没有指定results，使用之前爬取的内容
        if not args.get("results") and extracted_data.get("crawled_content"):
            args["results"] = extracted_data["crawled_content"]
            logger.info(f"Auto-filled analyze_search_results: {len(args['results'])} items")
            
        return args


class LLMSearchHostEnhanced:
    """
    增强版LLM搜索主机
    
    特点：
    1. 智能参数传导 - 工具调用结果自动传递给下一轮
    2. 完整的工具调用历史记录
    3. 增强的LLM上下文 - 包含历史工具调用信息
    4. 智能参数填充 - 自动填充缺失的参数
    """
    
    def __init__(
        self,
        model: str = None,
        infer_type: str = None,
        use_memory: bool = True,
        max_context_messages: int = 10,
    ):
        # 从配置文件读取默认模型配置
        if model is None or infer_type is None:
            host_config = MODEL_CONFIG.get("search", {}).get("host_llm", {})
            model = model or host_config.get("model", "gemini-2.0-flash-thinking-exp-01-21")
            infer_type = infer_type or host_config.get("infer_type", "OpenAI")

        self.model = model
        self.infer_type = infer_type
        self.use_memory = use_memory
        
        # 初始化LLM请求包装器
        self.request_wrapper = RequestWrapper(
            model=model,
            infer_type=infer_type,
            use_memory=use_memory,
            max_context_messages=max_context_messages
        )
        
        # 初始化工具组件
        self.result_extractor = ToolResultExtractor()
        self.parameter_filler = ParameterFiller()
        
        # 增强的搜索状态
        self.search_state = {
            "topic": "",
            "description": "",
            "current_round": 0,
            "max_rounds": 10,
            "completed": False,
            "completion_reason": "",
            
            # 工具调用历史
            "tool_call_history": [],
            "tool_results": {},  # 按工具类型分类的最新结果
            
            # 提取的关键数据
            "extracted_data": {
                "search_queries": [],
                "urls": [],
                "crawled_content": [],
                "analysis_results": []
            }
        }
        
        # MCP客户端
        self.mcp_client = None
        
        logger.info(f"LLMSearchHostEnhanced initialized with model: {model}, infer_type: {infer_type}")

    async def _create_mcp_client(self) -> MCPClient:
        """创建新的MCP客户端连接"""
        try:
            client = await create_mcp_client_from_config()
            logger.debug("Created new MCP client connection")
            return client
        except Exception as e:
            logger.error(f"Failed to create MCP client: {e}")
            raise

    def build_context_message(self) -> str:
        """构建包含工具调用历史的上下文消息"""
        context = f"""
## 搜索状态摘要 (第{self.search_state['current_round']}/{self.search_state['max_rounds']}轮)
主题: {self.search_state['topic']}
描述: {self.search_state['description']}

## 可用数据:
"""
        
        extracted = self.search_state['extracted_data']
        if extracted['search_queries']:
            context += f"- 已生成查询: {len(extracted['search_queries'])}个\n"
            context += f"  查询示例: {extracted['search_queries'][:3]}\n"
        if extracted['urls']:
            context += f"- 可用URLs: {len(extracted['urls'])}个\n"
        if extracted['crawled_content']:
            context += f"- 已爬取内容: {len(extracted['crawled_content'])}个\n"
        if extracted['analysis_results']:
            context += f"- 分析结果: {len(extracted['analysis_results'])}个\n"
        
        # 添加最近的工具调用历史
        recent_calls = self.search_state['tool_call_history'][-3:]  # 最近3次调用
        if recent_calls:
            context += "\n## 最近工具调用:\n"
            for call in recent_calls:
                status = "成功" if call.get('success', True) else "失败"
                context += f"- 第{call['round']}轮: {call['tool_name']} -> {status}\n"
                if call.get('extracted_data'):
                    context += f"  提取数据: {list(call['extracted_data'].keys())}\n"
        
        context += "\n请基于以上信息决定下一步操作。如果有可用数据，请充分利用。\n"

        return context

    def update_search_state(self, tool_name: str, tool_args: Dict[str, Any],
                           tool_result: Any, extracted_data: Dict[str, Any]):
        """更新搜索状态"""
        # 记录工具调用
        call_record = {
            "round": self.search_state["current_round"],
            "tool_name": tool_name,
            "tool_args": tool_args,
            "tool_result": tool_result,
            "extracted_data": extracted_data,
            "timestamp": datetime.now().isoformat(),
            "success": tool_result is not None
        }

        self.search_state["tool_call_history"].append(call_record)
        self.search_state["tool_results"][tool_name] = tool_result

        # 更新提取的数据
        if tool_name == "generate_search_queries" and extracted_data.get("queries"):
            self.search_state["extracted_data"]["search_queries"].extend(extracted_data["queries"])
            # 去重
            self.search_state["extracted_data"]["search_queries"] = list(set(
                self.search_state["extracted_data"]["search_queries"]
            ))

        elif tool_name == "web_search" and extracted_data.get("urls"):
            self.search_state["extracted_data"]["urls"].extend(extracted_data["urls"])
            # 去重
            self.search_state["extracted_data"]["urls"] = list(set(
                self.search_state["extracted_data"]["urls"]
            ))

        elif tool_name == "crawl_urls" and extracted_data.get("crawled_content"):
            self.search_state["extracted_data"]["crawled_content"].extend(extracted_data["crawled_content"])

        elif tool_name == "analyze_search_results" and extracted_data.get("analysis"):
            self.search_state["extracted_data"]["analysis_results"].append(extracted_data["analysis"])

        logger.debug(f"Updated search state after {tool_name}: "
                    f"queries={len(self.search_state['extracted_data']['search_queries'])}, "
                    f"urls={len(self.search_state['extracted_data']['urls'])}, "
                    f"content={len(self.search_state['extracted_data']['crawled_content'])}")

    async def execute_tool_with_context(self, tool_name: str, tool_args: Dict[str, Any]) -> Any:
        """执行工具调用，包含智能参数填充和结果提取"""
        try:
            # 1. 智能参数填充
            filled_args = self.parameter_filler.fill_params(
                tool_name, tool_args, self.search_state['extracted_data']
            )

            logger.info(f"Executing tool: {tool_name}")
            if filled_args != tool_args:
                logger.info(f"Parameters auto-filled: {list(set(filled_args.keys()) - set(tool_args.keys()))}")

            # 2. 创建临时MCP客户端执行工具（避免连接问题）
            client = await self._create_mcp_client()
            try:
                result = await client.call_tool(tool_name, filled_args)
            finally:
                await client.disconnect()

            # 3. 提取关键数据
            extracted = self.result_extractor.extract(tool_name, result)

            # 4. 更新search_state
            self.update_search_state(tool_name, filled_args, result, extracted)

            return result

        except Exception as e:
            logger.error(f"Tool execution failed: {tool_name}, error: {e}")
            # 记录失败的调用
            self.update_search_state(tool_name, tool_args, None, {})
            raise

    async def llm_decision_with_context(self, system_prompt: str) -> Dict[str, Any]:
        """LLM决策，包含完整的工具调用上下文"""
        try:
            # 构建包含工具调用历史的上下文
            context_message = self.build_context_message()

            # 完整的提示词
            full_prompt = f"{system_prompt}\n\n{context_message}"

            # 调用LLM
            response = self.request_wrapper.completion(full_prompt)

            # 解析JSON响应
            try:
                # 尝试提取JSON内容（去除markdown格式）
                if "```json" in response:
                    start = response.find("```json") + 7
                    end = response.find("```", start)
                    if end != -1:
                        response = response[start:end].strip()

                decision = json.loads(response)
                return decision
            except json.JSONDecodeError:
                logger.error(f"Failed to parse LLM response as JSON: {response}")
                return {"continue_search": False, "completion_reason": "LLM响应格式错误"}

        except Exception as e:
            logger.error(f"LLM decision failed: {e}")
            return {"continue_search": False, "completion_reason": f"LLM调用失败: {e}"}

    async def search(self, topic: str, description: str = "", max_rounds: int = 10) -> List[Dict[str, Any]]:
        """
        执行智能搜索

        Args:
            topic: 搜索主题
            description: 搜索描述
            max_rounds: 最大搜索轮数

        Returns:
            搜索结果列表
        """
        logger.info(f"🚀 开始智能搜索主题: '{topic}'")

        # 初始化搜索状态
        self.search_state.update({
            "topic": topic,
            "description": description,
            "current_round": 0,
            "max_rounds": max_rounds,
            "completed": False,
            "completion_reason": "",
            "tool_call_history": [],
            "tool_results": {},
            "extracted_data": {
                "search_queries": [],
                "urls": [],
                "crawled_content": [],
                "analysis_results": []
            }
        })

        try:
            # 第一轮：LLM搜索策略规划
            logger.info("📋 步骤1: LLM搜索策略规划和首个工具选择")

            strategy_prompt = f"""
你是一个智能搜索助手。请为以下搜索任务制定策略并选择第一个工具：

搜索主题: {topic}
搜索描述: {description}

可用工具:
1. generate_search_queries - 生成搜索查询
2. web_search - 网络搜索
3. crawl_urls - 爬取网页内容
4. analyze_search_results - 分析搜索结果

请返回JSON格式的策略，包含：
{{
    "task_understanding": "对搜索任务的理解",
    "search_strategy": "搜索策略描述",
    "first_tool": "第一个要使用的工具名称",
    "tool_arguments": {{"参数名": "参数值"}},
    "reasoning": "选择理由",
    "expected_outcome": "预期结果"
}}
"""

            strategy = await self.llm_decision_with_context(strategy_prompt)
            logger.info(f"搜索策略：{strategy}")

            # 执行第一个工具
            if strategy.get("first_tool") and strategy.get("tool_arguments"):
                logger.info(f"🔧 执行首个工具: {strategy['first_tool']}")
                await self.execute_tool_with_context(
                    strategy["first_tool"],
                    strategy["tool_arguments"]
                )

            # 多轮搜索循环
            for round_num in range(1, max_rounds):
                self.search_state["current_round"] = round_num
                logger.info(f"🔄 搜索轮次 {round_num + 1}/{max_rounds}")

                # LLM决策下一步
                decision_prompt = f"""
基于当前搜索状态，请决定下一步操作：

可用工具:
1. generate_search_queries - 生成搜索查询
2. web_search - 网络搜索
3. crawl_urls - 爬取网页内容
4. analyze_search_results - 分析搜索结果

请返回JSON格式的决策：
{{
    "continue_search": true/false,
    "completion_reason": "如果结束搜索，说明原因",
    "next_tool": "下一个工具名称",
    "tool_arguments": {{"参数名": "参数值"}},
    "reasoning": "决策理由",
    "progress_assessment": "当前进度评估"
}}

注意：如果有可用的数据（如URLs、查询等），请充分利用，不需要重复指定已有的参数。
"""

                decision = await self.llm_decision_with_context(decision_prompt)
                logger.info(f"LLM决策：{decision}")

                # 检查是否继续搜索
                if not decision.get("continue_search", True):
                    self.search_state["completed"] = True
                    self.search_state["completion_reason"] = decision.get("completion_reason", "LLM决定结束搜索")
                    logger.info(f"✅ LLM自主决定搜索完成: {self.search_state['completion_reason']}")
                    break

                # 执行下一个工具
                next_tool = decision.get("next_tool")
                tool_args = decision.get("tool_arguments", {})

                if next_tool:
                    logger.info(f"🔧 执行工具: {next_tool} with args: {tool_args}")
                    await self.execute_tool_with_context(next_tool, tool_args)
                else:
                    logger.warning("LLM未指定下一个工具，结束搜索")
                    break

            # 搜索完成
            if not self.search_state["completed"]:
                self.search_state["completed"] = True
                self.search_state["completion_reason"] = f"达到最大轮数限制 ({max_rounds})"

            # 返回爬取的内容作为最终结果
            results = self.search_state["extracted_data"]["crawled_content"]
            logger.info(f"🎉 搜索完成! 共{self.search_state['current_round'] + 1}轮，获得{len(results)}个结果")

            return results

        except Exception as e:
            logger.error(f"搜索过程中发生错误: {e}")
            return []

        finally:
            # 清理MCP客户端
            if self.mcp_client:
                await self.mcp_client.disconnect()


# 兼容性别名函数
def create_llm_search_host_enhanced(
    model: str = "gemini-2.0-flash-thinking-exp-01-21",
    infer_type: str = "OpenAI",
    **kwargs  # 忽略其他参数如max_workers
) -> LLMSearchHostEnhanced:
    """创建增强版LLM搜索主机实例（兼容性别名）"""
    return LLMSearchHostEnhanced(model=model, infer_type=infer_type)
