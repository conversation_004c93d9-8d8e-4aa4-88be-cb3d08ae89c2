{"servers": {"pipeline_orchestrator": {"command": "uv", "args": ["run", "python", "-m", "src.hidden.mcp_server.pipeline_orchestrator_server"], "env": {"PYTHONPATH": "/home/<USER>/LLMxMapReduce-v3/LLMxMapReduce_V2"}}, "digest_processor": {"command": "uv", "args": ["run", "python", "-m", "src.hidden.mcp_server.digest_processor_server"], "env": {"PYTHONPATH": "/home/<USER>/LLMxMapReduce-v3/LLMxMapReduce_V2"}}, "skeleton_processor": {"command": "uv", "args": ["run", "python", "-m", "src.hidden.mcp_server.skeleton_processor_server"], "env": {"PYTHONPATH": "/home/<USER>/LLMxMapReduce-v3/LLMxMapReduce_V2"}}, "group_processor": {"command": "uv", "args": ["run", "python", "-m", "src.hidden.mcp_server.group_processor_server"], "env": {"PYTHONPATH": "/home/<USER>/LLMxMapReduce-v3/LLMxMapReduce_V2"}}, "encode_processor": {"command": "uv", "args": ["run", "python", "-m", "src.encode.encode_processor_server"], "env": {"PYTHONPATH": "/home/<USER>/LLMxMapReduce-v3/LLMxMapReduce_V2"}}, "convolution_processor": {"command": "uv", "args": ["run", "python", "-m", "src.hidden.mcp_server.convolution_processor_server"], "env": {"PYTHONPATH": "/home/<USER>/LLMxMapReduce-v3/LLMxMapReduce_V2"}}, "basic_modules_processor": {"command": "uv", "args": ["run", "python", "-m", "src.hidden.mcp_server.basic_modules_processor_server"], "env": {"PYTHONPATH": "/home/<USER>/LLMxMapReduce-v3/LLMxMapReduce_V2"}}, "neurons_processor": {"command": "uv", "args": ["run", "python", "-m", "src.hidden.mcp_server.neurons_processor_server"], "env": {"PYTHONPATH": "/home/<USER>/LLMxMapReduce-v3/LLMxMapReduce_V2"}}}, "mcpServers": {"pipeline_orchestrator": {"command": "uv", "args": ["run", "python", "-m", "src.hidden.mcp_server.pipeline_orchestrator_server"], "env": {"PYTHONPATH": "/home/<USER>/LLMxMapReduce-v3/LLMxMapReduce_V2"}}, "digest_processor": {"command": "uv", "args": ["run", "python", "-m", "src.hidden.mcp_server.digest_processor_server"], "env": {"PYTHONPATH": "/home/<USER>/LLMxMapReduce-v3/LLMxMapReduce_V2"}}, "skeleton_processor": {"command": "uv", "args": ["run", "python", "-m", "src.hidden.mcp_server.skeleton_processor_server"], "env": {"PYTHONPATH": "/home/<USER>/LLMxMapReduce-v3/LLMxMapReduce_V2"}}, "group_processor": {"command": "uv", "args": ["run", "python", "-m", "src.hidden.mcp_server.group_processor_server"], "env": {"PYTHONPATH": "/home/<USER>/LLMxMapReduce-v3/LLMxMapReduce_V2"}}, "encode_processor": {"command": "uv", "args": ["run", "python", "-m", "src.encode.encode_processor_server"], "env": {"PYTHONPATH": "/home/<USER>/LLMxMapReduce-v3/LLMxMapReduce_V2"}}, "convolution_processor": {"command": "uv", "args": ["run", "python", "-m", "src.hidden.mcp_server.convolution_processor_server"], "env": {"PYTHONPATH": "/home/<USER>/LLMxMapReduce-v3/LLMxMapReduce_V2"}}, "basic_modules_processor": {"command": "uv", "args": ["run", "python", "-m", "src.hidden.mcp_server.basic_modules_processor_server"], "env": {"PYTHONPATH": "/home/<USER>/LLMxMapReduce-v3/LLMxMapReduce_V2"}}, "neurons_processor": {"command": "uv", "args": ["run", "python", "-m", "src.hidden.mcp_server.neurons_processor_server"], "env": {"PYTHONPATH": "/home/<USER>/LLMxMapReduce-v3/LLMxMapReduce_V2"}}}}