# 更新的MCP智能搜索系统使用指南

## 概述

基于您更新的MCP服务器，我已经相应地更新了客户端和主机代码，确保与程序接口完全兼容。

## 更新的功能

### 🆕 **新增的MCP工具**

您的MCP服务器现在提供以下工具：

1. **generate_search_queries** - 基于LLM生成优化的搜索查询
2. **web_search** - 使用提供的查询执行网络搜索
3. **analyze_search_results** - 分析和过滤搜索结果的相关性
4. **crawl_urls** - 异步爬取URL列表并处理内容

### 🔧 **更新的文件结构**

```
new/src/search/
├── llm_search_mcp_server.py           # 您更新的MCP服务器
├── llm_search_agent.py                # 更新的LLM对话代理
├── intelligent_llm_search_host.py     # 新的智能搜索主机
├── mcp_client.py                      # 纯粹的MCP通信客户端
└── ...

new/test_intelligent_search_flow.py    # 更新的测试脚本
new/docs/updated_mcp_usage.md          # 本文档
```

## 使用方式

### 1. 直接替换原有LLM_search

```python
# 原有代码
from src.LLM_search import LLM_search

# 替换为更新的智能版本
from src.search.intelligent_llm_search_host import LLM_search

# 其他代码保持完全不变！
retriever = LLM_search(model='gemini-2.0-flash-thinking-exp-01-21', ...)
queries = retriever.get_queries(topic=args.topic, description=args.description)
url_list = retriever.batch_web_search(queries=queries, topic=args.topic, top_n=args.top_n)
```

### 2. 新增的功能

#### URL爬取功能
```python
retriever = LLM_search()

# 新增：爬取URL列表
urls = ["https://example.com/paper1", "https://example.com/paper2"]
crawl_result = retriever.crawl_urls(urls, topic="机器学习")
print(crawl_result)
```

#### 智能结果分析
```python
# LLM会自动选择是否使用analyze_search_results工具
# 来提高搜索结果的相关性
url_list = retriever.batch_web_search(
    queries=queries, 
    topic=args.topic, 
    top_n=args.top_n
)
```

## 工作流程

### 🔄 **完整的智能搜索流程**

1. **Pipeline调用** → `LLM_search.get_queries()`
2. **LLM对话** → 分析任务："生成搜索查询"
3. **工具选择** → LLM选择 `generate_search_queries` 工具
4. **MCP调用** → 通过MCP客户端调用服务器
5. **原始逻辑** → 服务器执行原始MapxReduceV2搜索逻辑
6. **结果返回** → 保持与原接口完全一致

### 🧠 **LLM智能决策示例**

```json
{
    "analysis": "用户需要生成关于机器学习的搜索查询，应该使用generate_search_queries工具",
    "selected_tool": "llm_search_mcp",
    "tool_call": {
        "server": "llm_search_mcp",
        "tool": "generate_search_queries",
        "arguments": {
            "topic": "机器学习优化算法",
            "description": "研究机器学习中的优化技术",
            "model": "gemini-2.0-flash-thinking-exp-01-21"
        }
    },
    "execute": true
}
```

## 接口兼容性保证

### ✅ **完全兼容的方法**

| 原有方法 | 新版本 | 兼容性 |
|---------|--------|--------|
| `get_queries(topic, description)` | ✅ | 100%兼容 |
| `web_search(query)` | ✅ | 100%兼容 |
| `batch_web_search(queries, topic, top_n)` | ✅ | 100%兼容 |
| `snippet_filter(topic, snippet)` | ✅ | 100%兼容 |

### 🆕 **新增的方法**

| 新方法 | 功能 | 用途 |
|--------|------|------|
| `crawl_urls(urls, topic)` | URL爬取 | 获取网页内容 |
| `add_search_engine(name, config)` | 添加搜索引擎 | 扩展性支持 |
| `list_available_engines()` | 列出搜索引擎 | 查看可用工具 |

## 配置要求

### 环境变量
```bash
# LLM API密钥（至少设置一个）
export OPENAI_API_KEY="your-openai-key"
export GOOGLE_API_KEY="your-google-key"

# 搜索引擎API密钥（至少设置一个）
export SERP_API_KEY="your-serpapi-key"
export BING_SEARCH_V7_SUBSCRIPTION_KEY="your-bing-key"
```

### 可选依赖
```bash
# 如果需要使用crawl_urls功能
pip install crawl4ai

# MCP协议支持
pip install mcp
```

## 测试验证

### 运行完整测试
```bash
python test_intelligent_search_flow.py
```

### 测试覆盖
- ✅ LLM对话代理功能
- ✅ 智能LLM搜索接口兼容性
- ✅ 原有接口完全兼容
- ✅ 搜索引擎扩展性
- ✅ 端到端流水线模拟
- ✅ 增强MCP功能（新增）

## 扩展新的搜索引擎

### 1. 创建新的MCP服务器

```python
# 例如：academic_search_mcp_server.py
class AcademicSearchMCPServer:
    def __init__(self):
        self.server = Server("academic-search-server")
        self._register_tools()
    
    def _register_tools(self):
        @self.server.list_tools()
        async def handle_list_tools() -> List[Tool]:
            return [
                Tool(
                    name="academic_search",
                    description="Search academic papers and publications",
                    inputSchema={...}
                ),
                Tool(
                    name="citation_analysis", 
                    description="Analyze citation networks",
                    inputSchema={...}
                )
            ]
```

### 2. 注册到智能搜索系统

```python
from src.search.intelligent_llm_search_host import LLM_search

retriever = LLM_search()

# 添加新的搜索引擎
retriever.add_search_engine("academic_search_mcp", {
    "command": "python",
    "args": ["-m", "src.search.academic_search_mcp_server"],
    "env": {"PYTHONPATH": "."},
    "description": "学术搜索引擎，专门用于学术论文搜索",
    "tools": [
        "academic_search - 搜索学术论文和出版物",
        "citation_analysis - 分析引用网络"
    ]
})

# LLM现在可以智能选择学术搜索引擎
```

## 故障排除

### 1. MCP服务器连接问题
```bash
# 测试MCP服务器
python -m src.search.llm_search_mcp_server
```

### 2. 缺少依赖
```bash
# 检查crawl4ai
python -c "import crawl4ai; print('crawl4ai available')"

# 检查MCP
python -c "import mcp; print('mcp available')"
```

### 3. API密钥问题
```bash
# 验证API密钥
python -c "
import os
print('OpenAI:', bool(os.getenv('OPENAI_API_KEY')))
print('Google:', bool(os.getenv('GOOGLE_API_KEY')))
print('SERP:', bool(os.getenv('SERP_API_KEY')))
print('Bing:', bool(os.getenv('BING_SEARCH_V7_SUBSCRIPTION_KEY')))
"
```

## 性能优化

### 1. 异步处理
- 所有MCP调用都是异步的
- 支持并发处理多个搜索任务
- 智能连接管理和复用

### 2. 缓存机制
- LLM对话结果可以缓存
- 搜索结果可以缓存
- 减少重复的API调用

### 3. 错误恢复
- 自动重试机制
- 优雅的降级处理
- 详细的错误日志

## 总结

更新后的系统提供了：

1. **完全兼容** - 与原有LLM_search接口100%兼容
2. **功能增强** - 新增URL爬取和结果分析功能
3. **智能选择** - LLM根据任务智能选择最佳工具
4. **高度扩展** - 轻松添加新的搜索引擎和工具
5. **标准化** - 基于MCP协议的模块化架构

通过这个更新，您可以在保持原有系统稳定性的同时，获得更强大的搜索和分析能力。
