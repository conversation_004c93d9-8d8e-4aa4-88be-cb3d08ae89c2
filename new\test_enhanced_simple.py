#!/usr/bin/env python3
"""
简化测试增强版Host的实际搜索功能
专门验证工具调用参数传导是否解决了原有问题
"""

import asyncio
import logging
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'src'))

from src.search.llm_search_host_enhanced import LLMSearchHostEnhanced

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_enhanced_search():
    """测试增强版Host的实际搜索功能"""
    print("🚀 增强版Host实际搜索测试")
    print("="*50)
    
    try:
        # 初始化增强版Host
        print("📋 初始化增强版Host...")
        host = LLMSearchHostEnhanced()
        print("✅ 增强版Host初始化成功")
        
        # 执行简单搜索
        print("🔍 开始搜索测试...")
        print("主题: 'machine learning basics'")
        print("描述: '搜索机器学习基础知识'")
        print("最大轮数: 3 (限制以加快测试)")
        
        results = await host.search(
            topic="machine learning basics",
            description="搜索机器学习基础知识",
            max_rounds=3
        )
        
        print(f"\n🎉 搜索完成!")
        print(f"✅ 返回结果数量: {len(results)}")
        
        # 检查搜索状态
        print(f"\n📊 搜索状态分析:")
        print(f"  - 完成状态: {host.search_state['completed']}")
        print(f"  - 完成原因: {host.search_state['completion_reason']}")
        print(f"  - 总轮数: {host.search_state['current_round'] + 1}")
        print(f"  - 工具调用次数: {len(host.search_state['tool_call_history'])}")
        
        # 检查数据传导情况
        print(f"\n🔗 参数传导分析:")
        extracted_data = host.search_state['extracted_data']
        print(f"  - 生成的查询数: {len(extracted_data['search_queries'])}")
        print(f"  - 获取的URLs数: {len(extracted_data['urls'])}")
        print(f"  - 爬取的内容数: {len(extracted_data['crawled_content'])}")
        
        # 分析工具调用链
        print(f"\n🔧 工具调用链分析:")
        for i, call in enumerate(host.search_state['tool_call_history']):
            tool_name = call['tool_name']
            success = "✅" if call['success'] else "❌"
            extracted_keys = list(call.get('extracted_data', {}).keys())
            print(f"  {i+1}. {tool_name} {success}")
            if extracted_keys:
                print(f"     提取数据: {extracted_keys}")
        
        # 检查参数传导是否成功
        print(f"\n🎯 参数传导验证:")
        
        # 检查是否有查询生成 → 网络搜索的传导
        has_query_to_search = False
        has_search_to_crawl = False
        
        for call in host.search_state['tool_call_history']:
            if call['tool_name'] == 'web_search':
                args = call.get('tool_args', {})
                if args.get('queries') and len(args['queries']) > 0:
                    has_query_to_search = True
                    print(f"  ✅ 查询 → 网络搜索: 成功传导 {len(args['queries'])} 个查询")
            
            elif call['tool_name'] == 'crawl_urls':
                args = call.get('tool_args', {})
                if args.get('url_list') and len(args['url_list']) > 0:
                    has_search_to_crawl = True
                    print(f"  ✅ 网络搜索 → URL爬取: 成功传导 {len(args['url_list'])} 个URL")
        
        if not has_query_to_search:
            print("  ⚠️ 查询 → 网络搜索: 未检测到参数传导")
        if not has_search_to_crawl:
            print("  ⚠️ 网络搜索 → URL爬取: 未检测到参数传导")
        
        # 总结
        print(f"\n📈 测试总结:")
        if has_query_to_search or has_search_to_crawl:
            print("✅ 参数传导机制正常工作!")
            print("✅ 增强版Host成功解决了工具调用协同问题!")
        else:
            print("⚠️ 参数传导机制可能需要进一步优化")
        
        if len(results) > 0:
            print("✅ 搜索成功返回结果!")
            print("✅ 整体搜索流程正常!")
        else:
            print("⚠️ 搜索未返回结果，可能需要检查API配置或网络连接")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主测试函数"""
    print("🧪 增强版Host实际搜索功能测试")
    print("目标：验证工具调用参数传导是否解决了原有问题")
    print()
    
    success = await test_enhanced_search()
    
    if success:
        print("\n🎉 测试完成!")
        print("增强版Host已成功实现工具调用参数传导功能")
    else:
        print("\n❌ 测试失败!")
        print("需要进一步检查增强版Host的实现")

if __name__ == "__main__":
    asyncio.run(main())
