# 多模态推理文献搜索使用指南

## 📚 概述

本指南展示如何使用LLM_search Host通过MCP架构进行多模态推理综述文献的搜索。系统能够自动生成专业的学术查询、执行网络搜索、分析内容并提取相关信息。

## 🚀 快速开始

### 1. 环境准备

```bash
# 设置API密钥
export OPENAI_API_KEY="your_openai_api_key"
export SERP_API_KEY="your_serp_api_key"

# 或者在Windows中
set OPENAI_API_KEY=your_openai_api_key
set SERP_API_KEY=your_serp_api_key
```

### 2. 运行快速示例

```bash
# 运行快速搜索示例
python quick_multimodal_search_example.py

# 运行完整搜索示例
python example_multimodal_reasoning_search.py
```

## 💻 代码示例

### 基础使用

```python
from src.search.llm_search_host import LLM_search

# 创建搜索实例（使用配置文件中的默认模型）
llm_search = LLM_search()

# 定义搜索主题
topic = "多模态推理综述"
description = """
搜索多模态推理相关的综述文献，包括：
- 视觉语言模型
- 多模态大语言模型
- 跨模态理解与推理
"""

# 生成搜索查询
queries = llm_search.get_queries(topic=topic, description=description)
print(f"生成了 {len(queries)} 个查询")

# 执行搜索
urls = llm_search.batch_web_search(
    queries=queries,
    topic=topic,
    top_n=20
)
print(f"找到 {len(urls)} 个相关链接")

# 分析内容
results = llm_search.crawl_urls(
    topic=topic,
    url_list=urls,
    top_n=10,
    similarity_threshold=75
)
print(f"分析了 {len(results)} 篇文献")
```

### 高级使用

```python
# 自定义模型配置
llm_search = LLM_search(
    model="gpt-4",
    infer_type="OpenAI"
)

# 搜索特定子领域
subtopics = [
    "视觉语言预训练模型",
    "多模态大语言模型评估", 
    "具身智能多模态推理"
]

for subtopic in subtopics:
    queries = llm_search.get_queries(topic=subtopic)
    urls = llm_search.batch_web_search(queries, subtopic, top_n=15)
    results = llm_search.crawl_urls(subtopic, urls, top_n=8)
    print(f"{subtopic}: 找到 {len(results)} 篇文献")
```

## 🔧 配置说明

### 模型配置 (config/model_config.json)

```json
{
  "search": {
    "host_llm": {
      "model": "gemini-2.0-flash-thinking-exp-01-21",
      "infer_type": "OpenAI"
    },
    "query_generation": {
      "model": "gemini-2.0-flash-thinking-exp-01-21", 
      "infer_type": "OpenAI"
    },
    "content_analysis": {
      "model": "gemini-2.0-flash-thinking-exp-01-21",
      "infer_type": "OpenAI"
    }
  }
}
```

### MCP配置 (config/llm_search_mcp_config.json)

```json
{
  "servers": {
    "llm_search_server": {
      "env": {
        "OPENAI_API_KEY": "your_api_key",
        "SERP_API_KEY": "your_serp_key"
      }
    }
  },
  "server_config": {
    "default_engine": "google",
    "default_top_n": 20,
    "default_similarity_threshold": 80
  }
}
```

## 📊 搜索参数说明

### get_queries() 参数

- `topic`: 搜索主题（必需）
- `description`: 详细描述（可选）

### batch_web_search() 参数

- `queries`: 搜索查询列表
- `topic`: 主题
- `top_n`: 返回的URL数量（默认20）

### crawl_urls() 参数

- `topic`: 主题
- `url_list`: URL列表
- `top_n`: 返回的文献数量（默认80）
- `similarity_threshold`: 相似度阈值（默认80）
- `min_length`: 最小内容长度（默认350）
- `max_length`: 最大内容长度（默认20000）

## 🎯 搜索策略

### 学术文献搜索最佳实践

1. **精确的主题定义**
   ```python
   topic = "多模态推理综述"  # 明确、具体
   ```

2. **详细的描述**
   ```python
   description = """
   重点关注：
   1. 最新技术进展（2022-2024）
   2. 主流方法和模型
   3. 评估基准和数据集
   4. 应用场景和挑战
   """
   ```

3. **合理的参数设置**
   ```python
   # 学术搜索推荐参数
   top_n=30,  # 更多候选
   similarity_threshold=75,  # 较高阈值
   min_length=500,  # 确保内容质量
   max_length=25000  # 允许长文档
   ```

### 多领域搜索策略

```python
# 分层搜索：从宽泛到具体
topics = [
    "多模态推理综述",  # 总体综述
    "视觉语言模型",    # 核心技术
    "多模态评估方法",  # 评估方法
    "多模态应用案例"   # 应用实例
]

for topic in topics:
    # 执行搜索...
```

## 📈 结果分析

### 结果数据结构

```python
{
  "title": "文献标题",
  "url": "文献链接", 
  "score": 85.5,  # 相关度分数
  "content": "文献内容摘要...",
  "metadata": {
    "length": 1500,
    "source": "arxiv.org"
  }
}
```

### 结果过滤和排序

```python
# 按相关度排序
results.sort(key=lambda x: x.get('score', 0), reverse=True)

# 过滤高质量文献
high_quality = [r for r in results if r.get('score', 0) > 80]

# 按来源分类
arxiv_papers = [r for r in results if 'arxiv' in r.get('url', '')]
conference_papers = [r for r in results if any(conf in r.get('url', '') 
                     for conf in ['nips', 'icml', 'iclr', 'cvpr'])]
```

## 🛠️ 故障排除

### 常见问题

1. **API密钥错误**
   ```
   错误: Authentication failed
   解决: 检查OPENAI_API_KEY和SERP_API_KEY设置
   ```

2. **搜索结果为空**
   ```
   原因: 查询过于具体或网络问题
   解决: 调整搜索主题，检查网络连接
   ```

3. **内容分析失败**
   ```
   原因: 网页无法访问或内容格式问题
   解决: 调整similarity_threshold和length参数
   ```

### 调试技巧

```python
import logging
logging.basicConfig(level=logging.DEBUG)

# 查看详细日志
llm_search = LLM_search()
# 执行搜索...
```

## 🔄 扩展使用

### 自定义搜索引擎

```python
# 支持的搜索引擎
engines = llm_search.list_available_engines()
print(engines)  # ['google', 'bing', 'baidu']

# 使用特定引擎（通过配置文件设置）
```

### 批量主题搜索

```python
topics = [
    "多模态推理综述",
    "视觉问答系统", 
    "图像描述生成",
    "跨模态检索"
]

all_results = {}
for topic in topics:
    results = search_topic(topic)
    all_results[topic] = results

# 保存所有结果
import json
with open('all_multimodal_research.json', 'w') as f:
    json.dump(all_results, f, ensure_ascii=False, indent=2)
```

## 📝 总结

LLM_search Host提供了强大的学术文献搜索能力：

- ✅ **智能查询生成**: 基于LLM自动生成专业搜索查询
- ✅ **多引擎搜索**: 支持Google、Bing等多个搜索引擎
- ✅ **内容分析**: 自动分析网页内容，提取相关信息
- ✅ **灵活配置**: 支持自定义模型、参数和搜索策略
- ✅ **MCP架构**: 模块化设计，易于扩展和维护

适用于学术研究、技术调研、知识发现等多种场景。
