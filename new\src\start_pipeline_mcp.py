#!/usr/bin/env python3
"""
使用MCP协议的启动流水线
替代原有的start_pipeline.py，使用MCP版本的LLM搜索
"""

import os
import json
import logging
import gevent
import asyncio
import sys
from datetime import datetime
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent))

# 导入原有模块
try:
    from LLMxMapReduce_V2.async_d import Monitor, PipelineAnalyser, <PERSON><PERSON>ine
    from LLMxMapReduce_V2.src.decode.decode_pipeline import DecodePipeline
    from LLMxMapReduce_V2.src.encode.encode_pipeline import EncodePipeline
    from LLMxMapReduce_V2.src.hidden.hidden_pipeline import HiddenPipeline
    from LLMxMapReduce_V2.src.async_crawl import AsyncCrawler
    from LLMxMapReduce_V2.args import parse_args
except ImportError:
    # 如果在新版本目录结构中
    from async_d import Monitor, PipelineAnal<PERSON><PERSON>, Pipeline
    from src.decode.decode_pipeline import DecodePipeline
    from src.encode.encode_pipeline import Encode<PERSON>ipeline
    from src.hidden.hidden_pipeline import HiddenPipeline
    from src.async_crawl import AsyncCrawler
    from args import parse_args

# 导入MCP版本的LLM搜索
from search.llm_search_host import LLM_search

logger = logging.getLogger(__name__)


class EntirePipeline(Pipeline):
    """完整的处理流水线"""
    
    def __init__(self, args):
        with open(args.config_file, "r") as f:
            self.config = json.load(f)

        self.parallel_num = args.parallel_num
        self.encode_pipeline = EncodePipeline(
            self.config["encode"], args.data_num
        )
        self.hidden_pipeline = HiddenPipeline(
            self.config["hidden"],
            args.output_each_block,
            args.digest_group_mode,
            args.skeleton_group_size,
            args.block_count,
            args.conv_layer,
            args.conv_kernel_width,
            args.conv_result_num,
            args.top_k,
            args.self_refine_count,
            args.self_refine_best_of,
            worker_num=self.parallel_num,
        )
        self.decode_pipeline = DecodePipeline(
            self.config["decode"], args.output_file, worker_num=self.parallel_num
        )

        all_nodes = [self.encode_pipeline, self.hidden_pipeline, self.decode_pipeline]

        super().__init__(
            all_nodes, head=self.encode_pipeline, tail=self.decode_pipeline
        )

    def _connect_nodes(self):
        self.encode_pipeline >> self.hidden_pipeline >> self.decode_pipeline


def start_pipeline(args):
    httpx_logger = logging.getLogger("httpx")
    httpx_logger.setLevel(logging.WARNING)
    openai_logger = logging.getLogger("openai")
    openai_logger.setLevel(logging.WARNING)

    # start to write
    pipeline = EntirePipeline(args)

    pipeline_analyser = PipelineAnalyser()
    pipeline_analyser.register(pipeline)

    monitor = Monitor(report_interval=60)
    monitor.register(pipeline_analyser)
    monitor.start()

    pipeline.start()
    return pipeline


def main():
    """主函数 - 使用MCP版本的LLM搜索"""
    args = parse_args()
    logger.info(f"Start pipeline with args: {args}")
    logger.info(f"Current language: {os.environ.get('PROMPT_LANGUAGE', 'en')}")
    logger.info("🚀 Using MCP-based LLM Search")
    
    if args.topic:
        logger.info("set --topic, start to auto retrieve pages from Internet")
        
        try:
            # 使用MCP版本的LLM搜索
            logger.info("---------Start to generate queries (MCP).-------------")
            retriever = LLM_search(
                model='gemini-2.0-flash-thinking-exp-01-21', 
                infer_type="OpenAI", 
                engine='google', 
                each_query_result=10
            )
            
            # 生成查询
            queries = retriever.get_queries(topic=args.topic, description=args.description)
            logger.info(f"Generated {len(queries)} queries via MCP")
            
            # 执行搜索
            logger.info("---------Start to search pages (MCP).-------------")
            url_list = retriever.batch_web_search(
                queries=queries, 
                topic=args.topic, 
                top_n=int(args.top_n * 1.2)
            )
            logger.info(f"Retrieved {len(url_list)} URLs via MCP")
            
            # 生成输出路径
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            crawl_output_path = f"output/{args.topic}_{timestamp}_crawl_result.jsonl"
            if not os.path.exists(os.path.dirname(crawl_output_path)):
                os.makedirs(os.path.dirname(crawl_output_path), exist_ok=True)

            # 使用异步爬虫
            crawler = AsyncCrawler(model="gemini-2.0-flash-thinking-exp-01-21", infer_type="OpenAI")
            asyncio.run(
                crawler.run(
                    topic=args.topic,
                    url_list=url_list,
                    crawl_output_file_path=crawl_output_path,
                    top_n=args.top_n
                )
            )
            
            logger.info("---------References retrieve end (MCP).-------------")
            logger.info(f"Results saved to: {crawl_output_path}")
            
            # 启动处理流水线
            pipeline = start_pipeline(args)
            pipeline.put(crawl_output_path)
            
        except Exception as e:
            logger.error(f"Error in MCP-based search pipeline: {e}")
            logger.error("Falling back to original implementation...")
            raise
            
    elif args.input_file:
        logger.info("set --input_file, start to process the input file")
        pipeline = start_pipeline(args)
        pipeline.put(args.input_file)
    else:
        raise ValueError("Either --topic or --input_file should be set.")

    # 保持运行
    logger.info("Pipeline started, keeping alive...")
    while True:
        gevent.sleep(5)


def test_mcp_search():
    """测试MCP搜索功能"""
    logger.info("🧪 Testing MCP Search functionality...")
    
    try:
        # 创建MCP版本的LLM搜索实例
        retriever = LLM_search(
            model='gemini-2.0-flash-thinking-exp-01-21',
            infer_type="OpenAI",
            engine='google',
            each_query_result=5
        )
        
        # 测试查询生成
        logger.info("Testing query generation...")
        queries = retriever.get_queries(
            topic="artificial intelligence ethics",
            description="Research on ethical considerations in AI development"
        )
        logger.info(f"✅ Generated {len(queries)} queries: {queries}")
        
        # 测试批量搜索
        logger.info("Testing batch web search...")
        urls = retriever.batch_web_search(
            queries=queries[:2],  # 只测试前2个查询
            topic="artificial intelligence ethics",
            top_n=3
        )
        logger.info(f"✅ Retrieved {len(urls)} URLs: {urls}")
        
        logger.info("🎉 MCP Search test completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ MCP Search test failed: {e}")
        return False


if __name__ == "__main__":
    # 检查是否是测试模式
    if len(sys.argv) > 1 and sys.argv[1] == "--test":
        test_mcp_search()
    else:
        main()
