# 最终正确的MCP架构实现

## 用户反馈的核心要求

> "host不应该本身拥有被暴露的工具信息，而是通过向client发出询问，client调用server得到工具信息后反馈，host仅仅是一个llm的调用，应该仅仅包含提示词和llm选择调用方式，然后在此基础上你需要注意对齐一下原始llm_Search的接口。有关搜索的底层setting都应该在server中实现或者调取读入"

## 最终正确的架构实现

### ✅ Host (纯LLM调用器) - `llm_search_correct_host.py`

**职责**：
- ✅ 仅包含LLM调用逻辑和提示词
- ✅ 向Client询问Server的工具信息
- ✅ 使用LLM分析任务并选择合适的工具
- ✅ 通过Client调用Server工具
- ✅ 保持与原有LLM_search接口完全一致
- ✅ **不传递配置给Server（除用户参数外）**

**关键实现**：
```python
class LLM_search:
    def __init__(self, model, infer_type, engine, each_query_result, ...):
        # 仅创建LLM请求包装器（用于Host的LLM调用）
        self.request_wrapper = RequestWrapper(model=model, infer_type=infer_type)
        # 其他参数仅用于保持接口兼容性，实际配置由Server读取
    
    async def _get_available_tools(self):
        """向Server询问可用工具信息"""
        client = await self._get_mcp_client()
        return await client.list_tools()
    
    async def _llm_select_and_call_tool(self, task_description, **user_args):
        """LLM选择并调用工具"""
        # 1. 向Server询问可用工具
        available_tools = await self._get_available_tools()
        
        # 2. LLM选择工具
        prompt = self._create_tool_selection_prompt(task_description, available_tools)
        response = await self.request_wrapper.async_request(...)
        
        # 3. 调用工具（仅传递用户参数，不传递配置）
        client = await self._get_mcp_client()
        return await client.call_tool(selected_tool, user_args)
```

### ✅ Client (通信桥梁) - `llm_search_mcp_client.py`

**职责**：
- ✅ 纯粹的MCP协议通信
- ✅ 连接管理和消息传递
- ✅ 向Server询问工具信息
- ✅ 调用Server工具
- ✅ 返回Server响应

**关键实现**：
```python
class MCPClient:
    async def list_tools(self):
        """向Server询问可用工具信息"""
        tools = await self.session.list_tools()
        return [{"name": tool.name, "description": tool.description} for tool in tools.tools]
    
    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]):
        """调用Server工具"""
        result = await self.session.call_tool(CallToolRequest(name=tool_name, arguments=arguments))
        return json.loads(result.content[0].text)
```

### ✅ Server (工具+配置实现) - `llm_search_mcp_server.py`

**职责**：
- ✅ 实现具体的搜索工具
- ✅ **从配置文件读取所有配置**
- ✅ **使用默认配置值（不依赖Host传递）**
- ✅ 处理LLM调用
- ✅ 执行业务逻辑

**关键实现**：
```python
# Server启动时加载配置
def load_server_config():
    config_path = "config/llm_search_mcp_config.json"
    return {
        "default_model": "gemini-2.0-flash-thinking-exp-01-21",
        "default_engine": "google",
        "default_each_query_result": 10,
        "default_top_n": 20,
        # ... 其他配置
    }

SERVER_CONFIG = load_server_config()

# 工具函数使用Server配置
async def _generate_search_queries(topic: str, description: str = "", model: str = None):
    if model is None:
        model = SERVER_CONFIG["default_model"]  # 使用Server配置
    # ... 执行逻辑

async def _web_search(queries: List[str], topic: str, top_n: int = None, engine: str = None):
    if top_n is None:
        top_n = SERVER_CONFIG["default_top_n"]  # 使用Server配置
    if engine is None:
        engine = SERVER_CONFIG["default_engine"]  # 使用Server配置
    # ... 执行逻辑

# MCP工具调用不传递配置
@app.call_tool()
async def call_tool(name: str, arguments: Dict[str, Any]):
    if name == "generate_search_queries":
        result = await _generate_search_queries(
            arguments["topic"],
            arguments.get("description", ""),
            arguments.get("model")  # None表示使用Server配置
        )
    elif name == "web_search":
        result = await _web_search(
            arguments["queries"],
            arguments["topic"],
            arguments.get("top_n"),  # None表示使用Server配置
            arguments.get("engine")  # None表示使用Server配置
        )
```

## 正确的调用流程

```
1. Pipeline调用Host方法
   ↓
2. Host向Client询问Server可用工具
   Host: available_tools = await client.list_tools()
   ↓
3. Host使用LLM分析任务并选择工具
   Host: LLM分析 → 选择"generate_search_queries"
   ↓
4. Host通过Client调用Server工具（仅传递用户参数）
   Host: await client.call_tool("generate_search_queries", {"topic": "AI", "description": "..."})
   ↓
5. Server使用配置文件中的默认值执行工具
   Server: model = SERVER_CONFIG["default_model"]
   ↓
6. Host处理结果并返回给Pipeline
```

## 关键修正点对比

### ❌ 修正前的错误做法
```python
# Host传递配置给Server
result = await client.call_tool("web_search", {
    "queries": queries,
    "topic": topic,
    "engine": self.search_config["engine"],  # ❌ Host传递配置
    "top_n": self.search_config["each_query_result"]  # ❌ Host传递配置
})

# Server依赖Host传递的配置
async def _web_search(queries, topic, top_n, engine):
    # ❌ 依赖Host传递的参数
```

### ✅ 修正后的正确做法
```python
# Host仅传递用户参数
result = await client.call_tool("web_search", {
    "queries": queries,
    "topic": topic
    # ✅ 不传递配置，Server自己读取
})

# Server使用自己的配置
async def _web_search(queries, topic, top_n=None, engine=None):
    if top_n is None:
        top_n = SERVER_CONFIG["default_top_n"]  # ✅ 使用Server配置
    if engine is None:
        engine = SERVER_CONFIG["default_engine"]  # ✅ 使用Server配置
```

## 接口兼容性保证

```python
# 原有调用方式完全不变
from src.search.llm_search_correct_host import LLM_search

llm_search = LLM_search(
    model="gemini-2.0-flash-thinking-exp-01-21",
    engine="google",  # 兼容性参数，Server会忽略
    each_query_result=10  # 兼容性参数，Server会忽略
)

# API完全兼容
queries = llm_search.get_queries("机器学习", "研究机器学习算法")
urls = llm_search.batch_web_search(queries, "机器学习", 20)
```

## 配置文件示例

```json
{
  "servers": {
    "llm_search_server": {
      "command": "python",
      "args": ["-m", "src.search.llm_search_mcp_server"],
      "env": {
        "OPENAI_API_KEY": "your_key",
        "SERP_API_KEY": "your_key"
      }
    }
  },
  "server_config": {
    "default_model": "gemini-2.0-flash-thinking-exp-01-21",
    "default_engine": "google",
    "default_each_query_result": 10,
    "default_top_n": 20,
    "default_similarity_threshold": 80,
    "default_min_length": 350,
    "default_max_length": 20000
  }
}
```

## 使用方式

### 1. 直接使用（推荐）
```python
from src.search.llm_search_correct_host import LLM_search

# 创建实例（配置由Server读取）
llm_search = LLM_search()

# 使用（API与原版完全兼容）
queries = llm_search.get_queries("人工智能")
urls = llm_search.batch_web_search(queries, "人工智能", 15)
```

### 2. 测试验证
```bash
# 测试最终架构
python test_final_correct_architecture.py
```

## 总结

最终实现完全符合用户要求：

✅ **Host不拥有工具信息**：通过Client动态询问Server  
✅ **Host仅为LLM调用器**：只包含提示词和LLM选择逻辑  
✅ **配置在Server中**：所有搜索设置都在Server中实现  
✅ **接口对齐**：与原始LLM_search接口完全兼容  
✅ **职责清晰**：Host(LLM) → Client(通信) → Server(实现+配置)  

这种架构既满足了正确的MCP设计原则，又保持了完全的向后兼容性，真正实现了"Host仅仅是一个LLM的调用"的要求。
