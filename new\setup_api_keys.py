#!/usr/bin/env python3
"""
API密钥配置脚本
用于设置搜索引擎和LLM的API密钥
"""

import json
import os
from pathlib import Path

def setup_api_keys():
    """设置API密钥"""
    print("🔧 API密钥配置工具")
    print("=" * 50)
    
    # 配置文件路径
    config_path = Path("config/environment_config.json")
    
    # 加载现有配置
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
    except FileNotFoundError:
        print("❌ 配置文件不存在，请先运行主程序生成配置文件")
        return False
    except Exception as e:
        print(f"❌ 读取配置文件失败: {e}")
        return False
    
    print("📝 当前配置状态:")
    api_keys = config.get("api_keys", {})
    
    # 显示OpenAI配置状态
    openai_config = api_keys.get("openai", {})
    openai_key = openai_config.get("api_key", "")
    openai_url = openai_config.get("base_url", "")
    print(f"  OpenAI API Key: {'✅ 已配置' if openai_key else '❌ 未配置'}")
    print(f"  OpenAI Base URL: {openai_url if openai_url else '❌ 未配置'}")
    
    # 显示搜索引擎配置状态
    search_engines = api_keys.get("search_engines", {})
    serpapi_key = search_engines.get("serpapi_key", "")
    bing_key = search_engines.get("bing_subscription_key", "")
    
    print(f"  SerpAPI Key: {'✅ 已配置' if serpapi_key else '❌ 未配置'}")
    print(f"  Bing Search Key: {'✅ 已配置' if bing_key else '❌ 未配置'}")
    
    print("\n" + "=" * 50)
    print("🔑 API密钥配置选项:")
    print("1. 配置SerpAPI密钥 (推荐)")
    print("2. 配置Bing搜索密钥")
    print("3. 跳过搜索引擎配置 (使用模拟搜索)")
    print("4. 退出")
    
    while True:
        choice = input("\n请选择 (1-4): ").strip()
        
        if choice == "1":
            print("\n📋 SerpAPI配置:")
            print("1. 访问 https://serpapi.com/")
            print("2. 注册账户并获取API密钥")
            print("3. 免费账户每月有100次搜索额度")
            
            new_key = input("请输入SerpAPI密钥 (留空跳过): ").strip()
            if new_key:
                config["api_keys"]["search_engines"]["serpapi_key"] = new_key
                print("✅ SerpAPI密钥已设置")
            break
            
        elif choice == "2":
            print("\n📋 Bing搜索API配置:")
            print("1. 访问 https://www.microsoft.com/en-us/bing/apis/bing-web-search-api")
            print("2. 注册Azure账户并创建Bing搜索资源")
            print("3. 获取订阅密钥")
            
            new_key = input("请输入Bing搜索密钥 (留空跳过): ").strip()
            if new_key:
                config["api_keys"]["search_engines"]["bing_subscription_key"] = new_key
                print("✅ Bing搜索密钥已设置")
            break
            
        elif choice == "3":
            print("⚠️ 跳过搜索引擎配置，将使用模拟搜索功能")
            print("注意：模拟搜索只能返回示例数据，无法进行真实的网络搜索")
            break
            
        elif choice == "4":
            print("👋 退出配置")
            return False
            
        else:
            print("❌ 无效选择，请输入1-4")
    
    # 保存配置
    try:
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=4, ensure_ascii=False)
        print(f"\n✅ 配置已保存到 {config_path}")
        return True
    except Exception as e:
        print(f"\n❌ 保存配置失败: {e}")
        return False

def create_mock_search_config():
    """创建模拟搜索配置"""
    print("\n🔧 创建模拟搜索配置...")
    
    config_path = Path("config/environment_config.json")
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 添加模拟搜索标志
        config["search_settings"]["use_mock_search"] = True
        config["search_settings"]["mock_search_results"] = 5
        
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=4, ensure_ascii=False)
        
        print("✅ 模拟搜索配置已启用")
        return True
        
    except Exception as e:
        print(f"❌ 创建模拟搜索配置失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始API密钥配置")
    
    if not Path("config").exists():
        print("❌ config目录不存在，请确保在项目根目录运行此脚本")
        return
    
    success = setup_api_keys()
    
    if success:
        print("\n🎉 API密钥配置完成！")
        print("现在可以运行真实场景测试了")
    else:
        print("\n⚠️ 配置未完成，可以稍后重新运行此脚本")

if __name__ == "__main__":
    main()
